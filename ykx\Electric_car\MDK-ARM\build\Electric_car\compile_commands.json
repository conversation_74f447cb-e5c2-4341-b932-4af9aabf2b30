[{"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\LED_Buzzer.cpp", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --cpp --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\LED_Buzzer.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\LED_Buzzer.d .\\..\\Core\\Src\\LED_Buzzer.cpp"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\ZDT.cpp", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --cpp --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\ZDT.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\ZDT.d .\\..\\Core\\Src\\ZDT.cpp"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\app_state.cpp", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --cpp --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\app_state.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\app_state.d .\\..\\Core\\Src\\app_state.cpp"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\callback.cpp", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --cpp --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\callback.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\callback.d .\\..\\Core\\Src\\callback.cpp"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\dma.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\dma.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\dma.d .\\..\\Core\\Src\\dma.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\font.cpp", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --cpp --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\font.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\font.d .\\..\\Core\\Src\\font.cpp"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\freertos.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\freertos.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\freertos.d .\\..\\Core\\Src\\freertos.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\global_variable.cpp", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --cpp --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\global_variable.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\global_variable.d .\\..\\Core\\Src\\global_variable.cpp"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\gpio.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\gpio.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\gpio.d .\\..\\Core\\Src\\gpio.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\i2c.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\i2c.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\i2c.d .\\..\\Core\\Src\\i2c.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\jy901s.cpp", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --cpp --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\jy901s.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\jy901s.d .\\..\\Core\\Src\\jy901s.cpp"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\key.cpp", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --cpp --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\key.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\key.d .\\..\\Core\\Src\\key.cpp"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\main.cpp", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --cpp --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\main.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\main.d .\\..\\Core\\Src\\main.cpp"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\maixcam.cpp", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --cpp --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\maixcam.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\maixcam.d .\\..\\Core\\Src\\maixcam.cpp"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\motor.cpp", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --cpp --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\motor.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\motor.d .\\..\\Core\\Src\\motor.cpp"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\my_task.cpp", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --cpp --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\my_task.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\my_task.d .\\..\\Core\\Src\\my_task.cpp"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\oled.cpp", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --cpp --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\oled.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\oled.d .\\..\\Core\\Src\\oled.cpp"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\pid.cpp", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --cpp --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\pid.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\pid.d .\\..\\Core\\Src\\pid.cpp"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\servo.cpp", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --cpp --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\servo.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\servo.d .\\..\\Core\\Src\\servo.cpp"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\stm32f4xx_hal_msp.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\stm32f4xx_hal_msp.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\stm32f4xx_hal_msp.d .\\..\\Core\\Src\\stm32f4xx_hal_msp.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\stm32f4xx_hal_timebase_tim.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\stm32f4xx_hal_timebase_tim.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\stm32f4xx_hal_timebase_tim.d .\\..\\Core\\Src\\stm32f4xx_hal_timebase_tim.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\stm32f4xx_it.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\stm32f4xx_it.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\stm32f4xx_it.d .\\..\\Core\\Src\\stm32f4xx_it.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\system_stm32f4xx.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\system_stm32f4xx.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\system_stm32f4xx.d .\\..\\Core\\Src\\system_stm32f4xx.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\tim.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\tim.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\tim.d .\\..\\Core\\Src\\tim.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\truck.cpp", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --cpp --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\truck.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\truck.d .\\..\\Core\\Src\\truck.cpp"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\usart.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\usart.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\usart.d .\\..\\Core\\Src\\usart.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Core\\Src\\vofa.cpp", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --cpp --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Core\\Src\\vofa.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Core\\Src\\vofa.d .\\..\\Core\\Src\\vofa.cpp"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_cortex.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_cortex.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_cortex.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_cortex.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma_ex.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma_ex.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma_ex.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma_ex.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_exti.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_exti.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_exti.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_exti.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ex.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ex.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ex.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ex.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ramfunc.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ramfunc.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ramfunc.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ramfunc.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_gpio.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_gpio.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_gpio.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_gpio.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c_ex.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c_ex.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c_ex.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c_ex.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr_ex.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr_ex.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr_ex.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr_ex.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc_ex.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc_ex.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc_ex.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc_ex.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim_ex.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim_ex.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim_ex.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim_ex.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_uart.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_uart.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_uart.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_uart.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Middlewares\\Third_Party\\FreeRTOS\\Source\\CMSIS_RTOS_V2\\cmsis_os2.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Middlewares\\Third_Party\\FreeRTOS\\Source\\CMSIS_RTOS_V2\\cmsis_os2.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Middlewares\\Third_Party\\FreeRTOS\\Source\\CMSIS_RTOS_V2\\cmsis_os2.d .\\..\\Middlewares\\Third_Party\\FreeRTOS\\Source\\CMSIS_RTOS_V2\\cmsis_os2.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Middlewares\\Third_Party\\FreeRTOS\\Source\\croutine.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Middlewares\\Third_Party\\FreeRTOS\\Source\\croutine.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Middlewares\\Third_Party\\FreeRTOS\\Source\\croutine.d .\\..\\Middlewares\\Third_Party\\FreeRTOS\\Source\\croutine.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Middlewares\\Third_Party\\FreeRTOS\\Source\\event_groups.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Middlewares\\Third_Party\\FreeRTOS\\Source\\event_groups.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Middlewares\\Third_Party\\FreeRTOS\\Source\\event_groups.d .\\..\\Middlewares\\Third_Party\\FreeRTOS\\Source\\event_groups.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Middlewares\\Third_Party\\FreeRTOS\\Source\\list.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Middlewares\\Third_Party\\FreeRTOS\\Source\\list.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Middlewares\\Third_Party\\FreeRTOS\\Source\\list.d .\\..\\Middlewares\\Third_Party\\FreeRTOS\\Source\\list.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\MemMang\\heap_4.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\MemMang\\heap_4.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\MemMang\\heap_4.d .\\..\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\MemMang\\heap_4.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\RVDS\\ARM_CM4F\\port.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\RVDS\\ARM_CM4F\\port.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\RVDS\\ARM_CM4F\\port.d .\\..\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\RVDS\\ARM_CM4F\\port.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Middlewares\\Third_Party\\FreeRTOS\\Source\\queue.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Middlewares\\Third_Party\\FreeRTOS\\Source\\queue.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Middlewares\\Third_Party\\FreeRTOS\\Source\\queue.d .\\..\\Middlewares\\Third_Party\\FreeRTOS\\Source\\queue.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Middlewares\\Third_Party\\FreeRTOS\\Source\\stream_buffer.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Middlewares\\Third_Party\\FreeRTOS\\Source\\stream_buffer.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Middlewares\\Third_Party\\FreeRTOS\\Source\\stream_buffer.d .\\..\\Middlewares\\Third_Party\\FreeRTOS\\Source\\stream_buffer.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Middlewares\\Third_Party\\FreeRTOS\\Source\\tasks.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Middlewares\\Third_Party\\FreeRTOS\\Source\\tasks.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Middlewares\\Third_Party\\FreeRTOS\\Source\\tasks.d .\\..\\Middlewares\\Third_Party\\FreeRTOS\\Source\\tasks.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\Middlewares\\Third_Party\\FreeRTOS\\Source\\timers.c", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car -D\"USE_HAL_DRIVER\" -D\"STM32F407xx\" --cpu Cortex-M4.fp --li --c99 -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\Electric_car\\.obj\\__\\Middlewares\\Third_Party\\FreeRTOS\\Source\\timers.o --no_depend_system_headers --depend .\\build\\Electric_car\\.obj\\__\\Middlewares\\Third_Party\\FreeRTOS\\Source\\timers.d .\\..\\Middlewares\\Third_Party\\FreeRTOS\\Source\\timers.c"}, {"directory": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "file": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM\\startup_stm32f407xx.s", "command": "\"D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC\\bin\\armasm.exe\" --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I.cmsis/include -IRTE/_Electric_car --cpu Cortex-M4.fp --li -g -o .\\build\\Electric_car\\.obj\\startup_stm32f407xx.o --depend .\\build\\Electric_car\\.obj\\startup_stm32f407xx.d .\\startup_stm32f407xx.s"}]