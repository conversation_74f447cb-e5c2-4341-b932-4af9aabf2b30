Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    LED_Buzzer.o(i._ZN10LED_Buzzer2onEv) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    LED_Buzzer.o(i._ZN10LED_Buzzer3offEv) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    LED_Buzzer.o(i._ZN10LED_Buzzer4initEv) refers to LED_Buzzer.o(i._ZN10LED_Buzzer3offEv) for LED_Buzzer::off()
    LED_Buzzer.o(i._ZN10LED_Buzzer4turnEv) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    LED_Buzzer.o(i._ZN10LED_Buzzer8only_oneEj) refers to LED_Buzzer.o(i._ZN10LED_Buzzer2onEv) for LED_Buzzer::on()
    LED_Buzzer.o(i._ZN10LED_Buzzer8only_oneEj) refers to cmsis_os2.o(i.osDelay) for osDelay
    LED_Buzzer.o(i._ZN10LED_Buzzer8only_oneEj) refers to LED_Buzzer.o(i._ZN10LED_Buzzer3offEv) for LED_Buzzer::off()
    LED_Buzzer.o(.ARM.exidx) refers to LED_Buzzer.o(i._ZN10LED_BuzzerC1EP12GPIO_TypeDeft12Enable_level) for i._ZN10LED_BuzzerC1EP12GPIO_TypeDeft12Enable_level
    LED_Buzzer.o(.ARM.exidx) refers to LED_Buzzer.o(i._ZN10LED_Buzzer3offEv) for i._ZN10LED_Buzzer3offEv
    LED_Buzzer.o(.ARM.exidx) refers to LED_Buzzer.o(i._ZN10LED_Buzzer4initEv) for i._ZN10LED_Buzzer4initEv
    LED_Buzzer.o(.ARM.exidx) refers to LED_Buzzer.o(i._ZN10LED_Buzzer2onEv) for i._ZN10LED_Buzzer2onEv
    LED_Buzzer.o(.ARM.exidx) refers to LED_Buzzer.o(i._ZN10LED_Buzzer4turnEv) for i._ZN10LED_Buzzer4turnEv
    LED_Buzzer.o(.ARM.exidx) refers to LED_Buzzer.o(i._ZN10LED_Buzzer8only_oneEj) for i._ZN10LED_Buzzer8only_oneEj
    ZDT.o(i._ZN3ZDT13speed_controlEhthh) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    ZDT.o(i._ZN3ZDT13speed_controlEhthh) refers to ZDT.o(.data) for .data
    ZDT.o(i._ZN3ZDT15synchronizationEv) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    ZDT.o(i._ZN3ZDT15synchronizationEv) refers to ZDT.o(.data) for .data
    ZDT.o(i._ZN3ZDT16position_controlEhththh) refers to cxa_guard_acquire.o(i.__cxa_guard_acquire) for __cxa_guard_acquire
    ZDT.o(i._ZN3ZDT16position_controlEhththh) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    ZDT.o(i._ZN3ZDT16position_controlEhththh) refers to ZDT.o(.data) for .data
    ZDT.o(i._ZN3ZDT16position_controlEhththh) refers to ZDT.o(.bss) for .bss
    ZDT.o(i._ZN3ZDT4initEv) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    ZDT.o(i._ZN3ZDT4initEv) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ZDT.o(i._ZN3ZDT9deal_dataEv) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    ZDT.o(.ARM.exidx) refers to ZDT.o(i._ZN3ZDTC1EP20__UART_HandleTypeDefh) for i._ZN3ZDTC1EP20__UART_HandleTypeDefh
    ZDT.o(.ARM.exidx) refers to ZDT.o(i._ZN3ZDT13speed_controlEhthh) for i._ZN3ZDT13speed_controlEhthh
    ZDT.o(.ARM.exidx) refers to ZDT.o(i._ZN3ZDT16position_controlEhththh) for i._ZN3ZDT16position_controlEhththh
    ZDT.o(.ARM.exidx) refers to ZDT.o(i._ZN3ZDT15synchronizationEv) for i._ZN3ZDT15synchronizationEv
    ZDT.o(.ARM.exidx) refers to ZDT.o(i._ZN3ZDT4initEv) for i._ZN3ZDT4initEv
    ZDT.o(.ARM.exidx) refers to ZDT.o(i._ZN3ZDT9deal_dataEv) for i._ZN3ZDT9deal_dataEv
    app_state.o(i._Z7car_runf) refers to cmsis_os2.o(i.osDelay) for osDelay
    app_state.o(i._Z7car_runf) refers to global_variable.o(.bss) for distance
    app_state.o(i._Z7car_runf) refers to global_variable.o(.data) for car_state
    app_state.o(i._Z8car_turnf) refers to cmsis_os2.o(i.osDelay) for osDelay
    app_state.o(i._Z8car_turnf) refers to global_variable.o(.bss) for angle
    app_state.o(i._Z8car_turnf) refers to global_variable.o(.data) for car_state
    app_state.o(.ARM.exidx) refers to app_state.o(i._Z7car_runf) for i._Z7car_runf
    app_state.o(.ARM.exidx) refers to app_state.o(i._Z8car_turnf) for i._Z8car_turnf
    callback.o(i.HAL_UARTEx_RxEventCallback) refers to jy901s.o(i._ZN6jy901s6updateEv) for jy901s::update()
    callback.o(i.HAL_UARTEx_RxEventCallback) refers to jy901s.o(i._ZN6jy901s11sustain_yawEv) for jy901s::sustain_yaw()
    callback.o(i.HAL_UARTEx_RxEventCallback) refers to pid.o(i._ZN14PID_Controller10pid_figureEv) for PID_Controller::pid_figure()
    callback.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    callback.o(i.HAL_UARTEx_RxEventCallback) refers to vofa.o(i._ZN4vofa9read_dataEv) for vofa::read_data()
    callback.o(i.HAL_UARTEx_RxEventCallback) refers to maixcam.o(i._ZN7maixcam6updateEv) for maixcam::update()
    callback.o(i.HAL_UARTEx_RxEventCallback) refers to ZDT.o(i._ZN3ZDT16position_controlEhththh) for ZDT::position_control(unsigned char, unsigned short, unsigned char, unsigned short, unsigned char, unsigned char)
    callback.o(i.HAL_UARTEx_RxEventCallback) refers to ZDT.o(i._ZN3ZDT9deal_dataEv) for ZDT::deal_data()
    callback.o(i.HAL_UARTEx_RxEventCallback) refers to global_variable.o(.bss) for imu
    callback.o(i.HAL_UARTEx_RxEventCallback) refers to global_variable.o(.data) for draw_task_state
    callback.o(.ARM.exidx) refers to callback.o(i.HAL_UARTEx_RxEventCallback) for i.HAL_UARTEx_RxEventCallback
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    font.o(.constdata) refers to font.o(.constdata) for ascii_8x6
    font.o(.constdata) refers to font.o(.constdata) for ascii_12x6
    font.o(.constdata) refers to font.o(.constdata) for ascii_16x8
    font.o(.constdata) refers to font.o(.constdata) for ascii_24x12
    font.o(.constdata) refers to font.o(.constdata) for zh16x16
    font.o(.constdata) refers to font.o(.constdata) for afont16x8
    font.o(.constdata) refers to font.o(.constdata) for bilibiliData
    freertos.o(i.Encoder_Task) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(i.LED_Task) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(i.MX_FREERTOS_Init) refers to cmsis_os2.o(i.osThreadNew) for osThreadNew
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(.constdata) for .constdata
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(i.StartDefaultTask) for StartDefaultTask
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(.data) for .data
    freertos.o(i.MX_FREERTOS_Init) refers to my_task.o(i.LED_Task) for LED_Task
    freertos.o(i.MX_FREERTOS_Init) refers to my_task.o(i.Encoder_Task) for Encoder_Task
    freertos.o(i.MX_FREERTOS_Init) refers to my_task.o(i.main_Task) for main_Task
    freertos.o(i.MX_FREERTOS_Init) refers to my_task.o(i.vofa_Task) for vofa_Task
    freertos.o(i.MX_FREERTOS_Init) refers to my_task.o(i.OLED_Task) for OLED_Task
    freertos.o(i.MX_FREERTOS_Init) refers to my_task.o(i.draw_Task) for draw_Task
    freertos.o(i.OLED_Task) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(i.StartDefaultTask) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(i.draw_Task) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(i.main_Task) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(i.vofa_Task) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(.constdata) refers to freertos.o(.conststring) for .conststring
    freertos.o(.constdata) refers to freertos.o(.bss) for LEDControlBlock
    freertos.o(.constdata) refers to freertos.o(.bss) for LEDBuffer
    freertos.o(.constdata) refers to freertos.o(.bss) for encoderControlBlock
    freertos.o(.constdata) refers to freertos.o(.bss) for encoderBuffer
    freertos.o(.constdata) refers to freertos.o(.bss) for mainControlBlock
    freertos.o(.constdata) refers to freertos.o(.bss) for mainBuffer
    freertos.o(.constdata) refers to freertos.o(.bss) for vofaControlBlock
    freertos.o(.constdata) refers to freertos.o(.bss) for vofaBuffer
    freertos.o(.constdata) refers to freertos.o(.bss) for oledControlBlock
    freertos.o(.constdata) refers to freertos.o(.bss) for oledBuffer
    freertos.o(.constdata) refers to freertos.o(.bss) for drawControlBlock
    freertos.o(.constdata) refers to freertos.o(.bss) for drawBuffer
    global_variable.o(i._Z11system_initv) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    global_variable.o(i._Z11system_initv) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    global_variable.o(i._Z11system_initv) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    global_variable.o(i._Z11system_initv) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    global_variable.o(i._Z11system_initv) refers to tim.o(i.MX_TIM2_Init) for MX_TIM2_Init
    global_variable.o(i._Z11system_initv) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    global_variable.o(i._Z11system_initv) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    global_variable.o(i._Z11system_initv) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    global_variable.o(i._Z11system_initv) refers to usart.o(i.MX_USART3_UART_Init) for MX_USART3_UART_Init
    global_variable.o(i._Z11system_initv) refers to usart.o(i.MX_USART6_UART_Init) for MX_USART6_UART_Init
    global_variable.o(i._Z11system_initv) refers to tim.o(i.MX_TIM6_Init) for MX_TIM6_Init
    global_variable.o(i._Z11system_initv) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    global_variable.o(i._Z11system_initv) refers to maixcam.o(i._ZN7maixcam4initEv) for maixcam::init()
    global_variable.o(i._Z11system_initv) refers to vofa.o(i._ZN4vofa4initEv) for vofa::init()
    global_variable.o(i._Z11system_initv) refers to jy901s.o(i._ZN6jy901s4initEv) for jy901s::init()
    global_variable.o(i._Z11system_initv) refers to motor.o(i._ZN6TB66124initEv) for TB6612::init()
    global_variable.o(i._Z11system_initv) refers to oled.o(i._Z9OLED_Initv) for OLED_Init()
    global_variable.o(i._Z11system_initv) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    global_variable.o(i._Z11system_initv) refers to ZDT.o(i._ZN3ZDT4initEv) for ZDT::init()
    global_variable.o(i._Z11system_initv) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    global_variable.o(i._Z11system_initv) refers to global_variable.o(.bss) for .bss
    global_variable.o(i._Z11system_initv) refers to tim.o(.bss) for htim6
    global_variable.o(i.__sti___19_global_variable_cpp_f9d78f51) refers to jy901s.o(i._ZN6jy901sC1EP20__UART_HandleTypeDef) for jy901s::jy901s(__UART_HandleTypeDef*)
    global_variable.o(i.__sti___19_global_variable_cpp_f9d78f51) refers to motor.o(i._ZN6TB6612C1EP17TIM_HandleTypeDefjjP12GPIO_TypeDeftS3_tS3_tS3_tS1_S1_fffffbb) for TB6612::TB6612(TIM_HandleTypeDef*, unsigned, unsigned, GPIO_TypeDef*, unsigned short, GPIO_TypeDef*, unsigned short, GPIO_TypeDef*, unsigned short, GPIO_TypeDef*, unsigned short, TIM_HandleTypeDef*, TIM_HandleTypeDef*, float, float, float, float, float, bool, bool)
    global_variable.o(i.__sti___19_global_variable_cpp_f9d78f51) refers to truck.o(i._ZN5TruckC1Ev) for Truck::Truck()
    global_variable.o(i.__sti___19_global_variable_cpp_f9d78f51) refers to pid.o(i._ZN14PID_ControllerC1Efff8pid_modePfS1_S1_fbf) for PID_Controller::PID_Controller(float, float, float, pid_mode, float*, float*, float*, float, bool, float)
    global_variable.o(i.__sti___19_global_variable_cpp_f9d78f51) refers to maixcam.o(i._ZN7maixcamC1EP20__UART_HandleTypeDef) for maixcam::maixcam(__UART_HandleTypeDef*)
    global_variable.o(i.__sti___19_global_variable_cpp_f9d78f51) refers to vofa.o(i._ZN4vofaC1EP20__UART_HandleTypeDef) for vofa::vofa(__UART_HandleTypeDef*)
    global_variable.o(i.__sti___19_global_variable_cpp_f9d78f51) refers to key.o(i._ZN3KEYC1EP12GPIO_TypeDeft8KEY_Mode) for KEY::KEY(GPIO_TypeDef*, unsigned short, KEY_Mode)
    global_variable.o(i.__sti___19_global_variable_cpp_f9d78f51) refers to ZDT.o(i._ZN3ZDTC1EP20__UART_HandleTypeDefh) for ZDT::ZDT(__UART_HandleTypeDef*, unsigned char)
    global_variable.o(i.__sti___19_global_variable_cpp_f9d78f51) refers to usart.o(.bss) for huart2
    global_variable.o(i.__sti___19_global_variable_cpp_f9d78f51) refers to global_variable.o(.bss) for .bss
    global_variable.o(i.__sti___19_global_variable_cpp_f9d78f51) refers to tim.o(.bss) for htim3
    global_variable.o(i.__sti___19_global_variable_cpp_f9d78f51) refers to global_variable.o(.data) for .data
    global_variable.o(.ARM.exidx) refers to global_variable.o(i._Z11system_initv) for i._Z11system_initv
    global_variable.o(.ARM.exidx) refers to global_variable.o(i.__sti___19_global_variable_cpp_f9d78f51) for i.__sti___19_global_variable_cpp_f9d78f51
    global_variable.o(.init_array) refers to global_variable.o(i.__sti___19_global_variable_cpp_f9d78f51) for __sti___19_global_variable_cpp_f9d78f51
    global_variable.o(.init_array) refers to init_aeabi.o(.text) for __cpp_initialize__aeabi_
    gpio.o(i.MX_GPIO_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    i2c.o(i.HAL_I2C_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.MX_I2C1_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for .bss
    jy901s.o(i._ZN6jy901s4initEv) refers to jy901s.o(i._ZN6jy901s4zeroEv) for jy901s::zero()
    jy901s.o(i._ZN6jy901s4initEv) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    jy901s.o(i._ZN6jy901s4zeroEv) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    jy901s.o(.ARM.exidx) refers to jy901s.o(i._ZN6jy901sC1EP20__UART_HandleTypeDef) for i._ZN6jy901sC1EP20__UART_HandleTypeDef
    jy901s.o(.ARM.exidx) refers to jy901s.o(i._ZN6jy901s11sustain_yawEv) for i._ZN6jy901s11sustain_yawEv
    jy901s.o(.ARM.exidx) refers to jy901s.o(i._ZN6jy901s6updateEv) for i._ZN6jy901s6updateEv
    jy901s.o(.ARM.exidx) refers to jy901s.o(i._ZN6jy901s12reverse_dataEPf) for i._ZN6jy901s12reverse_dataEPf
    jy901s.o(.ARM.exidx) refers to jy901s.o(i._ZN6jy901s6getYawEv) for i._ZN6jy901s6getYawEv
    jy901s.o(.ARM.exidx) refers to jy901s.o(i._ZN6jy901s8getPitchEv) for i._ZN6jy901s8getPitchEv
    jy901s.o(.ARM.exidx) refers to jy901s.o(i._ZN6jy901s7getRollEv) for i._ZN6jy901s7getRollEv
    jy901s.o(.ARM.exidx) refers to jy901s.o(i._ZN6jy901s4zeroEv) for i._ZN6jy901s4zeroEv
    jy901s.o(.ARM.exidx) refers to jy901s.o(i._ZN6jy901s4initEv) for i._ZN6jy901s4initEv
    key.o(i._ZN3KEY9detectionEv) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key.o(i._ZN3KEY9detectionEv) refers to cmsis_os2.o(i.osDelay) for osDelay
    key.o(.ARM.exidx) refers to key.o(i._ZN3KEYC1EP12GPIO_TypeDeft8KEY_Mode) for i._ZN3KEYC1EP12GPIO_TypeDeft8KEY_Mode
    key.o(.ARM.exidx) refers to key.o(i._ZN3KEY9detectionEv) for i._ZN3KEY9detectionEv
    main.o(i.HAL_TIM_PeriodElapsedCallback) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    main.o(i.HAL_TIM_PeriodElapsedCallback) refers to global_variable.o(.data) for tick_ms
    main.o(i._Z18SystemClock_Configv) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i._Z18SystemClock_Configv) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i._Z18SystemClock_Configv) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i._Z18SystemClock_Configv) for SystemClock_Config()
    main.o(i.main) refers to global_variable.o(i._Z11system_initv) for system_init()
    main.o(i.main) refers to cmsis_os2.o(i.osKernelInitialize) for osKernelInitialize
    main.o(i.main) refers to freertos.o(i.MX_FREERTOS_Init) for MX_FREERTOS_Init
    main.o(i.main) refers to cmsis_os2.o(i.osKernelStart) for osKernelStart
    main.o(.ARM.exidx) refers to main.o(i.Error_Handler) for i.Error_Handler
    main.o(.ARM.exidx) refers to main.o(i._Z18SystemClock_Configv) for i._Z18SystemClock_Configv
    main.o(.ARM.exidx) refers to main.o(i.main) for i.main
    main.o(.ARM.exidx) refers to main.o(i.HAL_TIM_PeriodElapsedCallback) for i.HAL_TIM_PeriodElapsedCallback
    maixcam.o(i._ZN7maixcam4initEv) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    maixcam.o(i._ZN7maixcam6updateEv) refers to global_variable.o(.data) for purple
    maixcam.o(i._ZN7maixcam6updateEv) refers to global_variable.o(.data) for black
    maixcam.o(.ARM.exidx) refers to maixcam.o(i._ZN7maixcamC1EP20__UART_HandleTypeDef) for i._ZN7maixcamC1EP20__UART_HandleTypeDef
    maixcam.o(.ARM.exidx) refers to maixcam.o(i._ZN7maixcam4initEv) for i._ZN7maixcam4initEv
    maixcam.o(.ARM.exidx) refers to maixcam.o(i._ZN7maixcam6updateEv) for i._ZN7maixcam6updateEv
    motor.o(i._ZN6TB661214encoder_updateEv) refers to motor.o(.data) for .data
    motor.o(i._ZN6TB66124initEv) refers to motor.o(i._ZN6TB66128set_dutyEss) for TB6612::set_duty(short, short)
    motor.o(i._ZN6TB66124initEv) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    motor.o(i._ZN6TB66124initEv) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) for HAL_TIM_Encoder_Start
    motor.o(i._ZN6TB66128set_dutyEss) refers to motor.o(i._ZN6TB66129set_duty1Es) for TB6612::set_duty1(short)
    motor.o(i._ZN6TB66128set_dutyEss) refers to motor.o(i._ZN6TB66129set_duty2Es) for TB6612::set_duty2(short)
    motor.o(i._ZN6TB66129set_duty1Es) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor.o(i._ZN6TB66129set_duty2Es) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor.o(.ARM.exidx) refers to motor.o(i._ZN6TB6612C1EP17TIM_HandleTypeDefjjP12GPIO_TypeDeftS3_tS3_tS3_tS1_S1_fffffbb) for i._ZN6TB6612C1EP17TIM_HandleTypeDefjjP12GPIO_TypeDeftS3_tS3_tS3_tS1_S1_fffffbb
    motor.o(.ARM.exidx) refers to motor.o(i._ZN6TB66129set_duty1Es) for i._ZN6TB66129set_duty1Es
    motor.o(.ARM.exidx) refers to motor.o(i._ZN6TB66129set_duty2Es) for i._ZN6TB66129set_duty2Es
    motor.o(.ARM.exidx) refers to motor.o(i._ZN6TB66128set_dutyEss) for i._ZN6TB66128set_dutyEss
    motor.o(.ARM.exidx) refers to motor.o(i._ZN6TB66124initEv) for i._ZN6TB66124initEv
    motor.o(.ARM.exidx) refers to motor.o(i._ZN6TB661212get_duty_maxEv) for i._ZN6TB661212get_duty_maxEv
    motor.o(.ARM.exidx) refers to motor.o(i._ZN6TB661214encoder_updateEv) for i._ZN6TB661214encoder_updateEv
    motor.o(.ARM.exidx) refers to motor.o(i._ZN6TB661213zero_distanceEv) for i._ZN6TB661213zero_distanceEv
    my_task.o(i.Encoder_Task) refers to motor.o(i._ZN6TB661214encoder_updateEv) for TB6612::encoder_update()
    my_task.o(i.Encoder_Task) refers to pid.o(i._ZN14PID_Controller10pid_figureEv) for PID_Controller::pid_figure()
    my_task.o(i.Encoder_Task) refers to motor.o(i._ZN6TB66128set_dutyEss) for TB6612::set_duty(short, short)
    my_task.o(i.Encoder_Task) refers to cmsis_os2.o(i.osDelay) for osDelay
    my_task.o(i.Encoder_Task) refers to global_variable.o(.bss) for motor
    my_task.o(i.LED_Task) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    my_task.o(i.LED_Task) refers to cmsis_os2.o(i.osDelay) for osDelay
    my_task.o(i.OLED_Task) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    my_task.o(i.OLED_Task) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    my_task.o(i.OLED_Task) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    my_task.o(i.OLED_Task) refers to oled.o(i._Z13OLED_NewFramev) for OLED_NewFrame()
    my_task.o(i.OLED_Task) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    my_task.o(i.OLED_Task) refers to noretval__2sprintf.o(.text) for __2sprintf
    my_task.o(i.OLED_Task) refers to oled.o(i._Z16OLED_PrintStringhhPcPK4Font14OLED_ColorMode) for OLED_PrintString(unsigned char, unsigned char, char*, const Font*, OLED_ColorMode)
    my_task.o(i.OLED_Task) refers to oled.o(i._Z14OLED_ShowFramev) for OLED_ShowFrame()
    my_task.o(i.OLED_Task) refers to cmsis_os2.o(i.osDelay) for osDelay
    my_task.o(i.OLED_Task) refers to global_variable.o(.bss) for imu
    my_task.o(i.OLED_Task) refers to my_task.o(.bss) for .bss
    my_task.o(i.OLED_Task) refers to font.o(.constdata) for font16x16
    my_task.o(i.draw_Task) refers to ZDT.o(i._ZN3ZDT16position_controlEhththh) for ZDT::position_control(unsigned char, unsigned short, unsigned char, unsigned short, unsigned char, unsigned char)
    my_task.o(i.draw_Task) refers to cmsis_os2.o(i.osDelay) for osDelay
    my_task.o(i.draw_Task) refers to global_variable.o(.bss) for stepping_motor_up
    my_task.o(i.vofa_Task) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    my_task.o(i.vofa_Task) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    my_task.o(i.vofa_Task) refers to _printf_dec.o(.text) for _printf_int_dec
    my_task.o(i.vofa_Task) refers to noretval__2sprintf.o(.text) for __2sprintf
    my_task.o(i.vofa_Task) refers to strlen.o(.text) for strlen
    my_task.o(i.vofa_Task) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    my_task.o(i.vofa_Task) refers to cmsis_os2.o(i.osDelay) for osDelay
    my_task.o(i.vofa_Task) refers to global_variable.o(.bss) for stepping_motor_down
    my_task.o(i.vofa_Task) refers to my_task.o(.bss) for .bss
    my_task.o(.ARM.exidx) refers to my_task.o(i.LED_Task) for i.LED_Task
    my_task.o(.ARM.exidx) refers to my_task.o(i.Encoder_Task) for i.Encoder_Task
    my_task.o(.ARM.exidx) refers to my_task.o(i.main_Task) for i.main_Task
    my_task.o(.ARM.exidx) refers to my_task.o(i.vofa_Task) for i.vofa_Task
    my_task.o(.ARM.exidx) refers to my_task.o(i.OLED_Task) for i.OLED_Task
    my_task.o(.ARM.exidx) refers to my_task.o(i.draw_Task) for i.draw_Task
    oled.o(i._Z12OLED_SendCmdh) refers to oled.o(i._Z9OLED_SendPhh) for OLED_Send(unsigned char*, unsigned char)
    oled.o(i._Z12OLED_SendCmdh) refers to oled.o(.data) for .data
    oled.o(i._Z12OLED_SetBitshhh14OLED_ColorMode) refers to oled.o(i._Z17OLED_SetByte_Finehhhhh14OLED_ColorMode) for OLED_SetByte_Fine(unsigned char, unsigned char, unsigned char, unsigned char, unsigned char, OLED_ColorMode)
    oled.o(i._Z12OLED_SetBytehhh14OLED_ColorMode) refers to oled.o(.bss) for .bss
    oled.o(i._Z13OLED_DrawLinehhhh14OLED_ColorMode) refers to oled.o(i._Z13OLED_SetPixelhh14OLED_ColorMode) for OLED_SetPixel(unsigned char, unsigned char, OLED_ColorMode)
    oled.o(i._Z13OLED_DrawLinehhhh14OLED_ColorMode) refers to oled.o(.data) for .data
    oled.o(i._Z13OLED_NewFramev) refers to rt_memclr.o(.text) for __aeabi_memclr
    oled.o(i._Z13OLED_NewFramev) refers to oled.o(.bss) for .bss
    oled.o(i._Z13OLED_SetBlockhhPKhhh14OLED_ColorMode) refers to oled.o(i._Z12OLED_SetBitshhh14OLED_ColorMode) for OLED_SetBits(unsigned char, unsigned char, unsigned char, OLED_ColorMode)
    oled.o(i._Z13OLED_SetBlockhhPKhhh14OLED_ColorMode) refers to oled.o(i._Z17OLED_SetBits_Finehhhh14OLED_ColorMode) for OLED_SetBits_Fine(unsigned char, unsigned char, unsigned char, unsigned char, OLED_ColorMode)
    oled.o(i._Z13OLED_SetPixelhh14OLED_ColorMode) refers to oled.o(.bss) for .bss
    oled.o(i._Z14OLED_DrawImagehhPK5Image14OLED_ColorMode) refers to oled.o(i._Z13OLED_SetBlockhhPKhhh14OLED_ColorMode) for OLED_SetBlock(unsigned char, unsigned char, const unsigned char*, unsigned char, unsigned char, OLED_ColorMode)
    oled.o(i._Z14OLED_ShowFramev) refers to oled.o(i._Z12OLED_SendCmdh) for OLED_SendCmd(unsigned char)
    oled.o(i._Z14OLED_ShowFramev) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    oled.o(i._Z14OLED_ShowFramev) refers to oled.o(i._Z9OLED_SendPhh) for OLED_Send(unsigned char*, unsigned char)
    oled.o(i._Z14OLED_ShowFramev) refers to oled.o(.bss) for .bss
    oled.o(i._Z15OLED_DisPlay_Onv) refers to oled.o(i._Z12OLED_SendCmdh) for OLED_SendCmd(unsigned char)
    oled.o(i._Z15OLED_DrawCirclehhh14OLED_ColorMode) refers to oled.o(i._Z13OLED_SetPixelhh14OLED_ColorMode) for OLED_SetPixel(unsigned char, unsigned char, OLED_ColorMode)
    oled.o(i._Z16OLED_DisPlay_Offv) refers to oled.o(i._Z12OLED_SendCmdh) for OLED_SendCmd(unsigned char)
    oled.o(i._Z16OLED_DrawEllipsehhhh14OLED_ColorMode) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    oled.o(i._Z16OLED_DrawEllipsehhhh14OLED_ColorMode) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    oled.o(i._Z16OLED_DrawEllipsehhhh14OLED_ColorMode) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(i._Z16OLED_DrawEllipsehhhh14OLED_ColorMode) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i._Z16OLED_DrawEllipsehhhh14OLED_ColorMode) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    oled.o(i._Z16OLED_DrawEllipsehhhh14OLED_ColorMode) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    oled.o(i._Z16OLED_DrawEllipsehhhh14OLED_ColorMode) refers to oled.o(i._Z13OLED_SetPixelhh14OLED_ColorMode) for OLED_SetPixel(unsigned char, unsigned char, OLED_ColorMode)
    oled.o(i._Z16OLED_DrawEllipsehhhh14OLED_ColorMode) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    oled.o(i._Z16OLED_PrintStringhhPcPK4Font14OLED_ColorMode) refers to oled.o(i._Z16_OLED_GetUTF8LenPc) for _OLED_GetUTF8Len(char*)
    oled.o(i._Z16OLED_PrintStringhhPcPK4Font14OLED_ColorMode) refers to memcmp.o(.text) for memcmp
    oled.o(i._Z16OLED_PrintStringhhPcPK4Font14OLED_ColorMode) refers to oled.o(i._Z19OLED_PrintASCIICharhhcPK9ASCIIFont14OLED_ColorMode) for OLED_PrintASCIIChar(unsigned char, unsigned char, char, const ASCIIFont*, OLED_ColorMode)
    oled.o(i._Z16OLED_PrintStringhhPcPK4Font14OLED_ColorMode) refers to oled.o(i._Z13OLED_SetBlockhhPKhhh14OLED_ColorMode) for OLED_SetBlock(unsigned char, unsigned char, const unsigned char*, unsigned char, unsigned char, OLED_ColorMode)
    oled.o(i._Z17OLED_DrawTrianglehhhhhh14OLED_ColorMode) refers to oled.o(i._Z13OLED_DrawLinehhhh14OLED_ColorMode) for OLED_DrawLine(unsigned char, unsigned char, unsigned char, unsigned char, OLED_ColorMode)
    oled.o(i._Z17OLED_SetBits_Finehhhh14OLED_ColorMode) refers to oled.o(i._Z17OLED_SetByte_Finehhhhh14OLED_ColorMode) for OLED_SetByte_Fine(unsigned char, unsigned char, unsigned char, unsigned char, unsigned char, OLED_ColorMode)
    oled.o(i._Z17OLED_SetByte_Finehhhhh14OLED_ColorMode) refers to oled.o(.data) for .data
    oled.o(i._Z17OLED_SetByte_Finehhhhh14OLED_ColorMode) refers to oled.o(.bss) for .bss
    oled.o(i._Z17OLED_SetColorMode14OLED_ColorMode) refers to oled.o(i._Z12OLED_SendCmdh) for OLED_SendCmd(unsigned char)
    oled.o(i._Z18OLED_DrawRectanglehhhh14OLED_ColorMode) refers to oled.o(i._Z13OLED_DrawLinehhhh14OLED_ColorMode) for OLED_DrawLine(unsigned char, unsigned char, unsigned char, unsigned char, OLED_ColorMode)
    oled.o(i._Z19OLED_PrintASCIICharhhcPK9ASCIIFont14OLED_ColorMode) refers to oled.o(i._Z13OLED_SetBlockhhPKhhh14OLED_ColorMode) for OLED_SetBlock(unsigned char, unsigned char, const unsigned char*, unsigned char, unsigned char, OLED_ColorMode)
    oled.o(i._Z21OLED_DrawFilledCirclehhh14OLED_ColorMode) refers to oled.o(i._Z13OLED_SetPixelhh14OLED_ColorMode) for OLED_SetPixel(unsigned char, unsigned char, OLED_ColorMode)
    oled.o(i._Z21OLED_PrintASCIIStringhhPcPK9ASCIIFont14OLED_ColorMode) refers to oled.o(i._Z19OLED_PrintASCIICharhhcPK9ASCIIFont14OLED_ColorMode) for OLED_PrintASCIIChar(unsigned char, unsigned char, char, const ASCIIFont*, OLED_ColorMode)
    oled.o(i._Z23OLED_DrawFilledTrianglehhhhhh14OLED_ColorMode) refers to oled.o(i._Z13OLED_DrawLinehhhh14OLED_ColorMode) for OLED_DrawLine(unsigned char, unsigned char, unsigned char, unsigned char, OLED_ColorMode)
    oled.o(i._Z24OLED_DrawFilledRectanglehhhh14OLED_ColorMode) refers to oled.o(i._Z13OLED_DrawLinehhhh14OLED_ColorMode) for OLED_DrawLine(unsigned char, unsigned char, unsigned char, unsigned char, OLED_ColorMode)
    oled.o(i._Z9OLED_Initv) refers to oled.o(i._Z12OLED_SendCmdh) for OLED_SendCmd(unsigned char)
    oled.o(i._Z9OLED_Initv) refers to oled.o(i._Z13OLED_NewFramev) for OLED_NewFrame()
    oled.o(i._Z9OLED_Initv) refers to oled.o(i._Z14OLED_ShowFramev) for OLED_ShowFrame()
    oled.o(i._Z9OLED_SendPhh) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    oled.o(i._Z9OLED_SendPhh) refers to i2c.o(.bss) for hi2c1
    oled.o(.ARM.exidx) refers to oled.o(i._Z9OLED_SendPhh) for i._Z9OLED_SendPhh
    oled.o(.ARM.exidx) refers to oled.o(i._Z12OLED_SendCmdh) for i._Z12OLED_SendCmdh
    oled.o(.ARM.exidx) refers to oled.o(i._Z14OLED_ShowFramev) for i._Z14OLED_ShowFramev
    oled.o(.ARM.exidx) refers to oled.o(i._Z13OLED_NewFramev) for i._Z13OLED_NewFramev
    oled.o(.ARM.exidx) refers to oled.o(i._Z9OLED_Initv) for i._Z9OLED_Initv
    oled.o(.ARM.exidx) refers to oled.o(i._Z15OLED_DisPlay_Onv) for i._Z15OLED_DisPlay_Onv
    oled.o(.ARM.exidx) refers to oled.o(i._Z16OLED_DisPlay_Offv) for i._Z16OLED_DisPlay_Offv
    oled.o(.ARM.exidx) refers to oled.o(i._Z17OLED_SetColorMode14OLED_ColorMode) for i._Z17OLED_SetColorMode14OLED_ColorMode
    oled.o(.ARM.exidx) refers to oled.o(i._Z13OLED_SetPixelhh14OLED_ColorMode) for i._Z13OLED_SetPixelhh14OLED_ColorMode
    oled.o(.ARM.exidx) refers to oled.o(i._Z17OLED_SetByte_Finehhhhh14OLED_ColorMode) for i._Z17OLED_SetByte_Finehhhhh14OLED_ColorMode
    oled.o(.ARM.exidx) refers to oled.o(i._Z12OLED_SetBytehhh14OLED_ColorMode) for i._Z12OLED_SetBytehhh14OLED_ColorMode
    oled.o(.ARM.exidx) refers to oled.o(i._Z17OLED_SetBits_Finehhhh14OLED_ColorMode) for i._Z17OLED_SetBits_Finehhhh14OLED_ColorMode
    oled.o(.ARM.exidx) refers to oled.o(i._Z12OLED_SetBitshhh14OLED_ColorMode) for i._Z12OLED_SetBitshhh14OLED_ColorMode
    oled.o(.ARM.exidx) refers to oled.o(i._Z13OLED_SetBlockhhPKhhh14OLED_ColorMode) for i._Z13OLED_SetBlockhhPKhhh14OLED_ColorMode
    oled.o(.ARM.exidx) refers to oled.o(i._Z13OLED_DrawLinehhhh14OLED_ColorMode) for i._Z13OLED_DrawLinehhhh14OLED_ColorMode
    oled.o(.ARM.exidx) refers to oled.o(i._Z18OLED_DrawRectanglehhhh14OLED_ColorMode) for i._Z18OLED_DrawRectanglehhhh14OLED_ColorMode
    oled.o(.ARM.exidx) refers to oled.o(i._Z24OLED_DrawFilledRectanglehhhh14OLED_ColorMode) for i._Z24OLED_DrawFilledRectanglehhhh14OLED_ColorMode
    oled.o(.ARM.exidx) refers to oled.o(i._Z17OLED_DrawTrianglehhhhhh14OLED_ColorMode) for i._Z17OLED_DrawTrianglehhhhhh14OLED_ColorMode
    oled.o(.ARM.exidx) refers to oled.o(i._Z23OLED_DrawFilledTrianglehhhhhh14OLED_ColorMode) for i._Z23OLED_DrawFilledTrianglehhhhhh14OLED_ColorMode
    oled.o(.ARM.exidx) refers to oled.o(i._Z15OLED_DrawCirclehhh14OLED_ColorMode) for i._Z15OLED_DrawCirclehhh14OLED_ColorMode
    oled.o(.ARM.exidx) refers to oled.o(i._Z21OLED_DrawFilledCirclehhh14OLED_ColorMode) for i._Z21OLED_DrawFilledCirclehhh14OLED_ColorMode
    oled.o(.ARM.exidx) refers to oled.o(i._Z16OLED_DrawEllipsehhhh14OLED_ColorMode) for i._Z16OLED_DrawEllipsehhhh14OLED_ColorMode
    oled.o(.ARM.exidx) refers to oled.o(i._Z14OLED_DrawImagehhPK5Image14OLED_ColorMode) for i._Z14OLED_DrawImagehhPK5Image14OLED_ColorMode
    oled.o(.ARM.exidx) refers to oled.o(i._Z19OLED_PrintASCIICharhhcPK9ASCIIFont14OLED_ColorMode) for i._Z19OLED_PrintASCIICharhhcPK9ASCIIFont14OLED_ColorMode
    oled.o(.ARM.exidx) refers to oled.o(i._Z21OLED_PrintASCIIStringhhPcPK9ASCIIFont14OLED_ColorMode) for i._Z21OLED_PrintASCIIStringhhPcPK9ASCIIFont14OLED_ColorMode
    oled.o(.ARM.exidx) refers to oled.o(i._Z16_OLED_GetUTF8LenPc) for i._Z16_OLED_GetUTF8LenPc
    oled.o(.ARM.exidx) refers to oled.o(i._Z16OLED_PrintStringhhPcPK4Font14OLED_ColorMode) for i._Z16OLED_PrintStringhhPcPK4Font14OLED_ColorMode
    pid.o(i._ZN14PID_Controller10pid_figureEv) refers to pid.o(i._ZN14PID_Controller9dead_zoneEv) for PID_Controller::dead_zone()
    pid.o(.ARM.exidx) refers to pid.o(i._ZN14PID_ControllerC1Efff8pid_modePfS1_S1_fbf) for i._ZN14PID_ControllerC1Efff8pid_modePfS1_S1_fbf
    pid.o(.ARM.exidx) refers to pid.o(i._ZN14PID_Controller9dead_zoneEv) for i._ZN14PID_Controller9dead_zoneEv
    pid.o(.ARM.exidx) refers to pid.o(i._ZN14PID_Controller10pid_figureEv) for i._ZN14PID_Controller10pid_figureEv
    pid.o(.ARM.exidx) refers to pid.o(i._ZN14PID_Controller14integral_limitEf) for i._ZN14PID_Controller14integral_limitEf
    pid.o(.ARM.exidx) refers to pid.o(i._ZN14PID_Controller11deinit_dataEv) for i._ZN14PID_Controller11deinit_dataEv
    servo.o(.ARM.exidx) refers to servo.o(i._ZN5Servo9set_angleEt) for i._ZN5Servo9set_angleEt
    servo.o(.ARM.exidx) refers to servo.o(i._ZN5ServoC1EP17TIM_HandleTypeDefj) for i._ZN5ServoC1EP17TIM_HandleTypeDefj
    stm32f4xx_hal_msp.o(i.HAL_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig) for HAL_RCC_GetClockConfig
    stm32f4xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    stm32f4xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    stm32f4xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    stm32f4xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f4xx_hal_timebase_tim.o(.bss) for .bss
    stm32f4xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_timebase_tim.o(i.HAL_ResumeTick) refers to stm32f4xx_hal_timebase_tim.o(.bss) for .bss
    stm32f4xx_hal_timebase_tim.o(i.HAL_SuspendTick) refers to stm32f4xx_hal_timebase_tim.o(.bss) for .bss
    stm32f4xx_it.o(i.DMA1_Stream1_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream1_IRQHandler) refers to usart.o(.bss) for hdma_usart3_rx
    stm32f4xx_it.o(i.DMA1_Stream3_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream3_IRQHandler) refers to usart.o(.bss) for hdma_usart3_tx
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to usart.o(.bss) for hdma_usart2_rx
    stm32f4xx_it.o(i.DMA1_Stream6_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream6_IRQHandler) refers to usart.o(.bss) for hdma_usart2_tx
    stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler) refers to usart.o(.bss) for hdma_usart6_rx
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f4xx_it.o(i.DMA2_Stream6_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream6_IRQHandler) refers to usart.o(.bss) for hdma_usart6_tx
    stm32f4xx_it.o(i.DMA2_Stream7_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream7_IRQHandler) refers to usart.o(.bss) for hdma_usart1_tx
    stm32f4xx_it.o(i.I2C1_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) for HAL_I2C_ER_IRQHandler
    stm32f4xx_it.o(i.I2C1_ER_IRQHandler) refers to i2c.o(.bss) for hi2c1
    stm32f4xx_it.o(i.I2C1_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) for HAL_I2C_EV_IRQHandler
    stm32f4xx_it.o(i.I2C1_EV_IRQHandler) refers to i2c.o(.bss) for hi2c1
    stm32f4xx_it.o(i.TIM6_DAC_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(i.TIM6_DAC_IRQHandler) refers to tim.o(.bss) for htim6
    stm32f4xx_it.o(i.TIM8_TRG_COM_TIM14_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(i.TIM8_TRG_COM_TIM14_IRQHandler) refers to stm32f4xx_hal_timebase_tim.o(.bss) for htim14
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to usart.o(.bss) for huart3
    stm32f4xx_it.o(i.USART6_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART6_IRQHandler) refers to usart.o(.bss) for huart6
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_MspPostInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM1_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(i.MX_TIM1_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM2_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM3_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM6_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM6_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM6_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM6_Init) refers to tim.o(.bss) for .bss
    truck.o(i._ZN5Truck16sampling_sensorsEv) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    truck.o(i._ZN5Truck16sampling_sensorsEv) refers to truck.o(.data) for .data
    truck.o(.ARM.exidx) refers to truck.o(i._ZN5Truck16sampling_sensorsEv) for i._ZN5Truck16sampling_sensorsEv
    truck.o(.ARM.exidx) refers to truck.o(i._ZN5TruckC1Ev) for i._ZN5TruckC1Ev
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART3_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART3_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART3_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART6_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART6_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART6_UART_Init) refers to usart.o(.bss) for .bss
    vofa.o(i._ZN4vofa4initEv) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    vofa.o(i._ZN4vofa9debug_pidE14PID_Controller) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vofa.o(i._ZN4vofa9debug_pidE14PID_Controller) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vofa.o(i._ZN4vofa9debug_pidE14PID_Controller) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vofa.o(i._ZN4vofa9debug_pidE14PID_Controller) refers to _printf_dec.o(.text) for _printf_int_dec
    vofa.o(i._ZN4vofa9debug_pidE14PID_Controller) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vofa.o(i._ZN4vofa9debug_pidE14PID_Controller) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    vofa.o(i._ZN4vofa9debug_pidE14PID_Controller) refers to noretval__2sprintf.o(.text) for __2sprintf
    vofa.o(i._ZN4vofa9debug_pidE14PID_Controller) refers to strlen.o(.text) for strlen
    vofa.o(i._ZN4vofa9debug_pidE14PID_Controller) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    vofa.o(i._ZN4vofa9debug_pidE14PID_Controller) refers to vofa.o(.bss) for .bss
    vofa.o(i._ZN4vofa9read_dataEv) refers to vofa.o(i._ZN4vofa13analysis_dataEv) for vofa::analysis_data()
    vofa.o(i._ZN4vofa9read_dataEv) refers to global_variable.o(.bss) for distance
    vofa.o(.ARM.exidx) refers to vofa.o(i._ZN4vofaC1EP20__UART_HandleTypeDef) for i._ZN4vofaC1EP20__UART_HandleTypeDef
    vofa.o(.ARM.exidx) refers to vofa.o(i._ZN4vofa13analysis_dataEv) for i._ZN4vofa13analysis_dataEv
    vofa.o(.ARM.exidx) refers to vofa.o(i._ZN4vofa9read_dataEv) for i._ZN4vofa9read_dataEv
    vofa.o(.ARM.exidx) refers to vofa.o(i._ZN4vofa4initEv) for i._ZN4vofa4initEv
    vofa.o(.ARM.exidx) refers to vofa.o(i._ZN4vofa9debug_pidE14PID_Controller) for i._ZN4vofa9debug_pidE14PID_Controller
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to main.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to main.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to callback.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to callback.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to callback.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to callback.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    cmsis_os2.o(i.SysTick_Handler) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.SysTick_Handler) refers to port.o(i.xPortSysTickHandler) for xPortSysTickHandler
    cmsis_os2.o(i.TimerCallback) refers to timers.o(i.pvTimerGetTimerID) for pvTimerGetTimerID
    cmsis_os2.o(i.osDelay) refers to tasks.o(i.vTaskDelay) for vTaskDelay
    cmsis_os2.o(i.osDelayUntil) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osDelayUntil) refers to tasks.o(i.vTaskDelayUntil) for vTaskDelayUntil
    cmsis_os2.o(i.osEventFlagsClear) refers to event_groups.o(i.xEventGroupGetBitsFromISR) for xEventGroupGetBitsFromISR
    cmsis_os2.o(i.osEventFlagsClear) refers to event_groups.o(i.xEventGroupClearBitsFromISR) for xEventGroupClearBitsFromISR
    cmsis_os2.o(i.osEventFlagsClear) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    cmsis_os2.o(i.osEventFlagsDelete) refers to event_groups.o(i.vEventGroupDelete) for vEventGroupDelete
    cmsis_os2.o(i.osEventFlagsGet) refers to event_groups.o(i.xEventGroupGetBitsFromISR) for xEventGroupGetBitsFromISR
    cmsis_os2.o(i.osEventFlagsGet) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    cmsis_os2.o(i.osEventFlagsNew) refers to event_groups.o(i.xEventGroupCreateStatic) for xEventGroupCreateStatic
    cmsis_os2.o(i.osEventFlagsNew) refers to event_groups.o(i.xEventGroupCreate) for xEventGroupCreate
    cmsis_os2.o(i.osEventFlagsSet) refers to event_groups.o(i.xEventGroupSetBitsFromISR) for xEventGroupSetBitsFromISR
    cmsis_os2.o(i.osEventFlagsSet) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    cmsis_os2.o(i.osEventFlagsWait) refers to event_groups.o(i.xEventGroupWaitBits) for xEventGroupWaitBits
    cmsis_os2.o(i.osKernelGetInfo) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    cmsis_os2.o(i.osKernelGetState) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelGetState) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osKernelGetSysTimerCount) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osKernelGetSysTimerCount) refers to cmsis_os2.o(i.OS_Tick_GetCount) for OS_Tick_GetCount
    cmsis_os2.o(i.osKernelGetSysTimerFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    cmsis_os2.o(i.osKernelGetTickCount) refers to tasks.o(i.xTaskGetTickCountFromISR) for xTaskGetTickCountFromISR
    cmsis_os2.o(i.osKernelGetTickCount) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osKernelInitialize) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osKernelLock) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelLock) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    cmsis_os2.o(i.osKernelRestoreLock) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelRestoreLock) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    cmsis_os2.o(i.osKernelRestoreLock) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    cmsis_os2.o(i.osKernelStart) refers to tasks.o(i.vTaskStartScheduler) for vTaskStartScheduler
    cmsis_os2.o(i.osKernelStart) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osKernelUnlock) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelUnlock) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to cmsis_os2.o(i.AllocBlock) for AllocBlock
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to cmsis_os2.o(i.CreateBlock) for CreateBlock
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    cmsis_os2.o(i.osMemoryPoolDelete) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    cmsis_os2.o(i.osMemoryPoolDelete) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osMemoryPoolDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osMemoryPoolDelete) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    cmsis_os2.o(i.osMemoryPoolFree) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osMemoryPoolFree) refers to queue.o(i.xQueueGiveFromISR) for xQueueGiveFromISR
    cmsis_os2.o(i.osMemoryPoolFree) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osMemoryPoolFree) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    cmsis_os2.o(i.osMemoryPoolFree) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    cmsis_os2.o(i.osMemoryPoolFree) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osMemoryPoolGetCount) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osMemoryPoolGetCount) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osMemoryPoolGetSpace) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osMemoryPoolGetSpace) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osMemoryPoolNew) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    cmsis_os2.o(i.osMemoryPoolNew) refers to queue.o(i.xQueueCreateCountingSemaphoreStatic) for xQueueCreateCountingSemaphoreStatic
    cmsis_os2.o(i.osMemoryPoolNew) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osMessageQueueDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    cmsis_os2.o(i.osMessageQueueDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osMessageQueueGet) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os2.o(i.osMessageQueueGet) refers to queue.o(i.xQueueReceive) for xQueueReceive
    cmsis_os2.o(i.osMessageQueueGetCount) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osMessageQueueGetCount) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osMessageQueueGetSpace) refers to queue.o(i.uxQueueSpacesAvailable) for uxQueueSpacesAvailable
    cmsis_os2.o(i.osMessageQueueNew) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    cmsis_os2.o(i.osMessageQueueNew) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    cmsis_os2.o(i.osMessageQueueNew) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    cmsis_os2.o(i.osMessageQueuePut) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    cmsis_os2.o(i.osMessageQueuePut) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osMessageQueueReset) refers to queue.o(i.xQueueGenericReset) for xQueueGenericReset
    cmsis_os2.o(i.osMutexAcquire) refers to queue.o(i.xQueueTakeMutexRecursive) for xQueueTakeMutexRecursive
    cmsis_os2.o(i.osMutexAcquire) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    cmsis_os2.o(i.osMutexDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    cmsis_os2.o(i.osMutexDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osMutexGetOwner) refers to queue.o(i.xQueueGetMutexHolder) for xQueueGetMutexHolder
    cmsis_os2.o(i.osMutexNew) refers to queue.o(i.xQueueCreateMutexStatic) for xQueueCreateMutexStatic
    cmsis_os2.o(i.osMutexNew) refers to queue.o(i.xQueueCreateMutex) for xQueueCreateMutex
    cmsis_os2.o(i.osMutexNew) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    cmsis_os2.o(i.osMutexRelease) refers to queue.o(i.xQueueGiveMutexRecursive) for xQueueGiveMutexRecursive
    cmsis_os2.o(i.osMutexRelease) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osSemaphoreAcquire) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os2.o(i.osSemaphoreAcquire) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    cmsis_os2.o(i.osSemaphoreDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    cmsis_os2.o(i.osSemaphoreDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osSemaphoreGetCount) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osSemaphoreGetCount) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueCreateCountingSemaphoreStatic) for xQueueCreateCountingSemaphoreStatic
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueCreateCountingSemaphore) for xQueueCreateCountingSemaphore
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    cmsis_os2.o(i.osSemaphoreRelease) refers to queue.o(i.xQueueGiveFromISR) for xQueueGiveFromISR
    cmsis_os2.o(i.osSemaphoreRelease) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    cmsis_os2.o(i.osThreadEnumerate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.uxTaskGetSystemState) for uxTaskGetSystemState
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    cmsis_os2.o(i.osThreadEnumerate) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osThreadExit) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    cmsis_os2.o(i.osThreadFlagsClear) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    cmsis_os2.o(i.osThreadFlagsClear) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    cmsis_os2.o(i.osThreadFlagsGet) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    cmsis_os2.o(i.osThreadFlagsGet) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    cmsis_os2.o(i.osThreadFlagsSet) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    cmsis_os2.o(i.osThreadFlagsSet) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    cmsis_os2.o(i.osThreadFlagsWait) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osThreadFlagsWait) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    cmsis_os2.o(i.osThreadGetCount) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    cmsis_os2.o(i.osThreadGetId) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    cmsis_os2.o(i.osThreadGetName) refers to tasks.o(i.pcTaskGetName) for pcTaskGetName
    cmsis_os2.o(i.osThreadGetPriority) refers to tasks.o(i.uxTaskPriorityGet) for uxTaskPriorityGet
    cmsis_os2.o(i.osThreadGetStackSpace) refers to tasks.o(i.uxTaskGetStackHighWaterMark) for uxTaskGetStackHighWaterMark
    cmsis_os2.o(i.osThreadGetState) refers to tasks.o(i.eTaskGetState) for eTaskGetState
    cmsis_os2.o(i.osThreadNew) refers to tasks.o(i.xTaskCreateStatic) for xTaskCreateStatic
    cmsis_os2.o(i.osThreadNew) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    cmsis_os2.o(i.osThreadResume) refers to tasks.o(i.vTaskResume) for vTaskResume
    cmsis_os2.o(i.osThreadSetPriority) refers to tasks.o(i.vTaskPrioritySet) for vTaskPrioritySet
    cmsis_os2.o(i.osThreadSuspend) refers to tasks.o(i.vTaskSuspend) for vTaskSuspend
    cmsis_os2.o(i.osThreadTerminate) refers to tasks.o(i.eTaskGetState) for eTaskGetState
    cmsis_os2.o(i.osThreadTerminate) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    cmsis_os2.o(i.osTimerDelete) refers to timers.o(i.pvTimerGetTimerID) for pvTimerGetTimerID
    cmsis_os2.o(i.osTimerDelete) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    cmsis_os2.o(i.osTimerDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osTimerGetName) refers to timers.o(i.pcTimerGetName) for pcTimerGetName
    cmsis_os2.o(i.osTimerIsRunning) refers to timers.o(i.xTimerIsTimerActive) for xTimerIsTimerActive
    cmsis_os2.o(i.osTimerNew) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    cmsis_os2.o(i.osTimerNew) refers to timers.o(i.xTimerCreateStatic) for xTimerCreateStatic
    cmsis_os2.o(i.osTimerNew) refers to timers.o(i.xTimerCreate) for xTimerCreate
    cmsis_os2.o(i.osTimerNew) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osTimerNew) refers to cmsis_os2.o(i.TimerCallback) for TimerCallback
    cmsis_os2.o(i.osTimerStart) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    cmsis_os2.o(i.osTimerStop) refers to timers.o(i.xTimerIsTimerActive) for xTimerIsTimerActive
    cmsis_os2.o(i.osTimerStop) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    cmsis_os2.o(i.vApplicationGetIdleTaskMemory) refers to cmsis_os2.o(.bss) for .bss
    cmsis_os2.o(i.vApplicationGetTimerTaskMemory) refers to cmsis_os2.o(.bss) for .bss
    event_groups.o(i.vEventGroupClearBitsCallback) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.vEventGroupDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.vEventGroupSetBitsCallback) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupClearBitsFromISR) refers to timers.o(i.xTimerPendFunctionCallFromISR) for xTimerPendFunctionCallFromISR
    event_groups.o(i.xEventGroupClearBitsFromISR) refers to event_groups.o(i.vEventGroupClearBitsCallback) for vEventGroupClearBitsCallback
    event_groups.o(i.xEventGroupCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    event_groups.o(i.xEventGroupCreate) refers to list.o(i.vListInitialise) for vListInitialise
    event_groups.o(i.xEventGroupCreateStatic) refers to list.o(i.vListInitialise) for vListInitialise
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSetBitsFromISR) refers to timers.o(i.xTimerPendFunctionCallFromISR) for xTimerPendFunctionCallFromISR
    event_groups.o(i.xEventGroupSetBitsFromISR) refers to event_groups.o(i.vEventGroupSetBitsCallback) for vEventGroupSetBitsCallback
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSync) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupWaitBits) refers to event_groups.o(i.prvTestWaitCondition) for prvTestWaitCondition
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    heap_4.o(i.prvHeapInit) refers to heap_4.o(.bss) for .bss
    heap_4.o(i.prvHeapInit) refers to heap_4.o(.data) for .data
    heap_4.o(i.prvInsertBlockIntoFreeList) refers to heap_4.o(.data) for .data
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(i.prvHeapInit) for prvHeapInit
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(i.prvInsertBlockIntoFreeList) for prvInsertBlockIntoFreeList
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(.data) for .data
    heap_4.o(i.vPortFree) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.vPortFree) refers to heap_4.o(i.prvInsertBlockIntoFreeList) for prvInsertBlockIntoFreeList
    heap_4.o(i.vPortFree) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.vPortFree) refers to heap_4.o(.data) for .data
    heap_4.o(i.vPortGetHeapStats) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.vPortGetHeapStats) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.vPortGetHeapStats) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    heap_4.o(i.vPortGetHeapStats) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    heap_4.o(i.vPortGetHeapStats) refers to heap_4.o(.data) for .data
    heap_4.o(i.xPortGetFreeHeapSize) refers to heap_4.o(.data) for .data
    heap_4.o(i.xPortGetMinimumEverFreeHeapSize) refers to heap_4.o(.data) for .data
    port.o(.emb_text) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    port.o(.emb_text) refers to tasks.o(.data) for pxCurrentTCB
    port.o(i.prvTaskExitError) refers to port.o(.data) for .data
    port.o(i.pxPortInitialiseStack) refers to port.o(i.prvTaskExitError) for prvTaskExitError
    port.o(i.vPortEndScheduler) refers to port.o(.data) for .data
    port.o(i.vPortEnterCritical) refers to port.o(.data) for .data
    port.o(i.vPortExitCritical) refers to port.o(.data) for .data
    port.o(i.vPortSetupTimerInterrupt) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    port.o(i.vPortValidateInterruptPriority) refers to port.o(.emb_text) for vPortGetIPSR
    port.o(i.vPortValidateInterruptPriority) refers to port.o(.data) for .data
    port.o(i.xPortStartScheduler) refers to port.o(i.vPortSetupTimerInterrupt) for vPortSetupTimerInterrupt
    port.o(i.xPortStartScheduler) refers to port.o(.emb_text) for __asm___6_port_c_39a90d8d__prvEnableVFP
    port.o(i.xPortStartScheduler) refers to port.o(.data) for .data
    port.o(i.xPortSysTickHandler) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    queue.o(i.pcQueueGetName) refers to queue.o(.bss) for .bss
    queue.o(i.prvCopyDataFromQueue) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    queue.o(i.prvCopyDataToQueue) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    queue.o(i.prvCopyDataToQueue) refers to tasks.o(i.xTaskPriorityDisinherit) for xTaskPriorityDisinherit
    queue.o(i.prvInitialiseMutex) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    queue.o(i.prvInitialiseNewQueue) refers to queue.o(i.xQueueGenericReset) for xQueueGenericReset
    queue.o(i.prvIsQueueEmpty) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvIsQueueEmpty) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.vTaskMissedYield) for vTaskMissedYield
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueAddToRegistry) refers to queue.o(.bss) for .bss
    queue.o(i.vQueueDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    queue.o(i.vQueueDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    queue.o(i.vQueueUnregisterQueue) refers to queue.o(.bss) for .bss
    queue.o(i.vQueueWaitForMessageRestricted) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.vQueueWaitForMessageRestricted) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueWaitForMessageRestricted) refers to tasks.o(i.vTaskPlaceOnEventListRestricted) for vTaskPlaceOnEventListRestricted
    queue.o(i.vQueueWaitForMessageRestricted) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueCreateCountingSemaphore) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    queue.o(i.xQueueCreateCountingSemaphoreStatic) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    queue.o(i.xQueueCreateMutex) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    queue.o(i.xQueueCreateMutex) refers to queue.o(i.prvInitialiseMutex) for prvInitialiseMutex
    queue.o(i.xQueueCreateMutexStatic) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    queue.o(i.xQueueCreateMutexStatic) refers to queue.o(i.prvInitialiseMutex) for prvInitialiseMutex
    queue.o(i.xQueueGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    queue.o(i.xQueueGenericCreate) refers to queue.o(i.prvInitialiseNewQueue) for prvInitialiseNewQueue
    queue.o(i.xQueueGenericCreateStatic) refers to queue.o(i.prvInitialiseNewQueue) for prvInitialiseNewQueue
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericReset) refers to list.o(i.vListInitialise) for vListInitialise
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericReset) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvCopyDataToQueue) for prvCopyDataToQueue
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueGenericSendFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueueGenericSendFromISR) refers to queue.o(i.prvCopyDataToQueue) for prvCopyDataToQueue
    queue.o(i.xQueueGenericSendFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGetMutexHolder) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGetMutexHolder) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGiveFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueueGiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGiveMutexRecursive) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    queue.o(i.xQueueGiveMutexRecursive) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueuePeek) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueuePeek) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueuePeek) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueuePeek) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueuePeek) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueuePeekFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueuePeekFromISR) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueReceive) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueReceive) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueReceive) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueReceiveFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueueReceiveFromISR) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.pvTaskIncrementMutexHeldCount) for pvTaskIncrementMutexHeldCount
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskPriorityInherit) for xTaskPriorityInherit
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskPriorityDisinheritAfterTimeout) for vTaskPriorityDisinheritAfterTimeout
    queue.o(i.xQueueTakeMutexRecursive) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    queue.o(i.xQueueTakeMutexRecursive) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    stream_buffer.o(i.prvInitialiseNewStreamBuffer) refers to memset.o(.text) for memset
    stream_buffer.o(i.prvInitialiseNewStreamBuffer) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stream_buffer.o(i.prvReadBytesFromBuffer) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    stream_buffer.o(i.prvReadMessageFromBuffer) refers to stream_buffer.o(i.prvReadBytesFromBuffer) for prvReadBytesFromBuffer
    stream_buffer.o(i.prvWriteBytesToBuffer) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    stream_buffer.o(i.prvWriteMessageToBuffer) refers to stream_buffer.o(i.prvWriteBytesToBuffer) for prvWriteBytesToBuffer
    stream_buffer.o(i.vStreamBufferDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    stream_buffer.o(i.vStreamBufferDelete) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stream_buffer.o(i.xStreamBufferBytesAvailable) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    stream_buffer.o(i.xStreamBufferGenericCreate) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferGenericCreateStatic) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferIsFull) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferNextMessageLengthBytes) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferNextMessageLengthBytes) refers to stream_buffer.o(i.prvReadBytesFromBuffer) for prvReadBytesFromBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferReceive) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskNotifyStateClear) for xTaskNotifyStateClear
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    stream_buffer.o(i.xStreamBufferReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    stream_buffer.o(i.xStreamBufferReceive) refers to stream_buffer.o(i.prvReadMessageFromBuffer) for prvReadMessageFromBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.xStreamBufferReceiveCompletedFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to stream_buffer.o(i.prvReadMessageFromBuffer) for prvReadMessageFromBuffer
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferReset) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferReset) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferReset) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.vTaskSetTimeOutState) for vTaskSetTimeOutState
    stream_buffer.o(i.xStreamBufferSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskNotifyStateClear) for xTaskNotifyStateClear
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    stream_buffer.o(i.xStreamBufferSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.prvWriteMessageToBuffer) for prvWriteMessageToBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.xStreamBufferSendCompletedFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.prvWriteMessageToBuffer) for prvWriteMessageToBuffer
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    tasks.o(i.eTaskGetState) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.eTaskGetState) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.eTaskGetState) refers to tasks.o(.data) for .data
    tasks.o(i.eTaskGetState) refers to tasks.o(.bss) for .bss
    tasks.o(i.pcTaskGetName) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvAddNewTaskToReadyList) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvAddNewTaskToReadyList) refers to list.o(i.vListInitialise) for vListInitialise
    tasks.o(i.prvAddNewTaskToReadyList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.prvAddNewTaskToReadyList) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvDeleteTCB) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.prvIdleTask) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvIdleTask) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvIdleTask) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvIdleTask) refers to tasks.o(i.prvDeleteTCB) for prvDeleteTCB
    tasks.o(i.prvIdleTask) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvIdleTask) refers to tasks.o(.data) for .data
    tasks.o(i.prvInitialiseNewTask) refers to aeabi_memset.o(.text) for __aeabi_memset
    tasks.o(i.prvInitialiseNewTask) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    tasks.o(i.prvInitialiseNewTask) refers to port.o(i.pxPortInitialiseStack) for pxPortInitialiseStack
    tasks.o(i.prvListTasksWithinSingleList) refers to tasks.o(i.vTaskGetInfo) for vTaskGetInfo
    tasks.o(i.prvResetNextTaskUnblockTime) refers to tasks.o(.data) for .data
    tasks.o(i.prvTaskIsTaskSuspended) refers to tasks.o(.bss) for .bss
    tasks.o(i.pvTaskIncrementMutexHeldCount) refers to tasks.o(.data) for .data
    tasks.o(i.ulTaskNotifyTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.ulTaskNotifyTake) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.ulTaskNotifyTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.ulTaskNotifyTake) refers to tasks.o(.data) for .data
    tasks.o(i.ulTaskNotifyValueClear) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.ulTaskNotifyValueClear) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.ulTaskNotifyValueClear) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetNumberOfTasks) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetStackHighWaterMark) refers to tasks.o(i.prvTaskCheckFreeStackSpace) for prvTaskCheckFreeStackSpace
    tasks.o(i.uxTaskGetStackHighWaterMark) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(i.prvListTasksWithinSingleList) for prvListTasksWithinSingleList
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(.bss) for .bss
    tasks.o(i.uxTaskPriorityGet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.uxTaskPriorityGet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.uxTaskPriorityGet) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskPriorityGetFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.uxTaskPriorityGetFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskResetEventItemValue) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelay) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.vTaskDelay) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskDelay) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskDelay) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskDelete) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskDelete) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskDelete) refers to tasks.o(i.prvDeleteTCB) for prvDeleteTCB
    tasks.o(i.vTaskDelete) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskDelete) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelete) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskEndScheduler) refers to port.o(i.vPortEndScheduler) for vPortEndScheduler
    tasks.o(i.vTaskEndScheduler) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.eTaskGetState) for eTaskGetState
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.prvTaskCheckFreeStackSpace) for prvTaskCheckFreeStackSpace
    tasks.o(i.vTaskGetInfo) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskInternalSetTimeOutState) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskMissedYield) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskNotifyGiveFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.vTaskNotifyGiveFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskNotifyGiveFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskNotifyGiveFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskNotifyGiveFromISR) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskPlaceOnEventList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskPrioritySet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskPrioritySet) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskPrioritySet) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPrioritySet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskPrioritySet) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPrioritySet) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskResume) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskResume) refers to tasks.o(i.prvTaskIsTaskSuspended) for prvTaskIsTaskSuspended
    tasks.o(i.vTaskResume) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskResume) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskResume) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskResume) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskResume) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSetTimeOutState) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskStartScheduler) refers to cmsis_os2.o(i.vApplicationGetIdleTaskMemory) for vApplicationGetIdleTaskMemory
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.xTaskCreateStatic) for xTaskCreateStatic
    tasks.o(i.vTaskStartScheduler) refers to timers.o(i.xTimerCreateTimerTask) for xTimerCreateTimerTask
    tasks.o(i.vTaskStartScheduler) refers to port.o(i.xPortStartScheduler) for xPortStartScheduler
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.prvIdleTask) for prvIdleTask
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSuspend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSuspend) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskSuspend) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskSuspend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSuspend) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.vTaskSuspend) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    tasks.o(i.vTaskSuspend) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSuspend) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskSuspendAll) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    tasks.o(i.xTaskCreate) refers to tasks.o(i.prvInitialiseNewTask) for prvInitialiseNewTask
    tasks.o(i.xTaskCreate) refers to tasks.o(i.prvAddNewTaskToReadyList) for prvAddNewTaskToReadyList
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.xTaskCreateStatic) refers to tasks.o(i.prvInitialiseNewTask) for prvInitialiseNewTask
    tasks.o(i.xTaskCreateStatic) refers to tasks.o(i.prvAddNewTaskToReadyList) for prvAddNewTaskToReadyList
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskGenericNotify) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskGenericNotify) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskGenericNotifyFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.xTaskGenericNotifyFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskGenericNotifyFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskGetCurrentTaskHandle) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetSchedulerState) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetTickCount) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetTickCountFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.xTaskGetTickCountFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.xTaskIncrementTick) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskIncrementTick) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskNotifyStateClear) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskNotifyStateClear) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskNotifyStateClear) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskNotifyWait) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskNotifyWait) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.xTaskNotifyWait) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskNotifyWait) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityDisinherit) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskPriorityDisinherit) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskPriorityDisinherit) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityDisinherit) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskPriorityInherit) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskPriorityInherit) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskPriorityInherit) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityInherit) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskRemoveFromEventList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskRemoveFromEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskResumeAll) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskResumeAll) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskResumeFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(i.prvTaskIsTaskSuspended) for prvTaskIsTaskSuspended
    tasks.o(i.xTaskResumeFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskResumeFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(.bss) for .bss
    timers.o(i.prvCheckForValidListAndQueue) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.prvCheckForValidListAndQueue) refers to list.o(i.vListInitialise) for vListInitialise
    timers.o(i.prvCheckForValidListAndQueue) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    timers.o(i.prvCheckForValidListAndQueue) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    timers.o(i.prvCheckForValidListAndQueue) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.prvCheckForValidListAndQueue) refers to timers.o(.data) for .data
    timers.o(i.prvCheckForValidListAndQueue) refers to timers.o(.bss) for .bss
    timers.o(i.prvInitialiseNewTimer) refers to timers.o(i.prvCheckForValidListAndQueue) for prvCheckForValidListAndQueue
    timers.o(i.prvInitialiseNewTimer) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    timers.o(i.prvInsertTimerInActiveList) refers to list.o(i.vListInsert) for vListInsert
    timers.o(i.prvInsertTimerInActiveList) refers to timers.o(.data) for .data
    timers.o(i.prvProcessReceivedCommands) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.prvSampleTimeNow) for prvSampleTimeNow
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.prvInsertTimerInActiveList) for prvInsertTimerInActiveList
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    timers.o(i.prvProcessReceivedCommands) refers to heap_4.o(i.vPortFree) for vPortFree
    timers.o(i.prvProcessReceivedCommands) refers to queue.o(i.xQueueReceive) for xQueueReceive
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(.data) for .data
    timers.o(i.prvProcessTimerOrBlockTask) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(i.prvSampleTimeNow) for prvSampleTimeNow
    timers.o(i.prvProcessTimerOrBlockTask) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    timers.o(i.prvProcessTimerOrBlockTask) refers to queue.o(i.vQueueWaitForMessageRestricted) for vQueueWaitForMessageRestricted
    timers.o(i.prvProcessTimerOrBlockTask) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(i.prvInsertTimerInActiveList) for prvInsertTimerInActiveList
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(.data) for .data
    timers.o(i.prvSampleTimeNow) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    timers.o(i.prvSampleTimeNow) refers to timers.o(i.prvSwitchTimerLists) for prvSwitchTimerLists
    timers.o(i.prvSampleTimeNow) refers to timers.o(.data) for .data
    timers.o(i.prvSwitchTimerLists) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvSwitchTimerLists) refers to list.o(i.vListInsert) for vListInsert
    timers.o(i.prvSwitchTimerLists) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    timers.o(i.prvSwitchTimerLists) refers to timers.o(.data) for .data
    timers.o(i.prvTimerTask) refers to timers.o(i.prvProcessTimerOrBlockTask) for prvProcessTimerOrBlockTask
    timers.o(i.prvTimerTask) refers to timers.o(i.prvProcessReceivedCommands) for prvProcessReceivedCommands
    timers.o(i.prvTimerTask) refers to timers.o(.data) for .data
    timers.o(i.pvTimerGetTimerID) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.pvTimerGetTimerID) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.uxTimerGetReloadMode) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.uxTimerGetReloadMode) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.vTimerSetReloadMode) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.vTimerSetReloadMode) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.vTimerSetTimerID) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.vTimerSetTimerID) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    timers.o(i.xTimerCreate) refers to timers.o(i.prvInitialiseNewTimer) for prvInitialiseNewTimer
    timers.o(i.xTimerCreateStatic) refers to timers.o(i.prvInitialiseNewTimer) for prvInitialiseNewTimer
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(i.prvCheckForValidListAndQueue) for prvCheckForValidListAndQueue
    timers.o(i.xTimerCreateTimerTask) refers to cmsis_os2.o(i.vApplicationGetTimerTaskMemory) for vApplicationGetTimerTaskMemory
    timers.o(i.xTimerCreateTimerTask) refers to tasks.o(i.xTaskCreateStatic) for xTaskCreateStatic
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(.data) for .data
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(i.prvTimerTask) for prvTimerTask
    timers.o(i.xTimerGenericCommand) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    timers.o(i.xTimerGenericCommand) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    timers.o(i.xTimerGenericCommand) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    timers.o(i.xTimerGenericCommand) refers to timers.o(.data) for .data
    timers.o(i.xTimerGetTimerDaemonTaskHandle) refers to timers.o(.data) for .data
    timers.o(i.xTimerIsTimerActive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.xTimerIsTimerActive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerPendFunctionCall) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    timers.o(i.xTimerPendFunctionCall) refers to timers.o(.data) for .data
    timers.o(i.xTimerPendFunctionCallFromISR) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    timers.o(i.xTimerPendFunctionCallFromISR) refers to timers.o(.data) for .data
    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to port.o(.emb_text) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to cmsis_os2.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream1_IRQHandler) for DMA1_Stream1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream3_IRQHandler) for DMA1_Stream3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) for DMA1_Stream5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream6_IRQHandler) for DMA1_Stream6_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.I2C1_EV_IRQHandler) for I2C1_EV_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.I2C1_ER_IRQHandler) for I2C1_ER_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.TIM8_TRG_COM_TIM14_IRQHandler) for TIM8_TRG_COM_TIM14_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.TIM6_DAC_IRQHandler) for TIM6_DAC_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler) for DMA2_Stream1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream6_IRQHandler) for DMA2_Stream6_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream7_IRQHandler) for DMA2_Stream7_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART6_IRQHandler) for USART6_IRQHandler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    memset.o(.text) refers to rt_memclr.o(.text) for _memset
    aeabi_memset.o(.text) refers to rt_memclr.o(.text) for _memset
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    init_aeabi.o(.emb_text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000031) for __rt_lib_init_cpp_2
    init_aeabi.o(.init_array) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000031) for __rt_lib_init_cpp_2
    init_aeabi.o(.init_array) refers to init_aeabi.o(.text) for __cpp_initialize__aeabi_
    init_aeabi.o(.dummy_text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000031) for __rt_lib_init_cpp_2
    init_aeabi.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000031) for __rt_lib_init_cpp_2
    init_aeabi.o(.ARM.exidx) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000031) for __rt_lib_init_cpp_2
    init_aeabi.o(.ARM.exidx) refers to init_aeabi.o(.text) for .text
    cxa_guard_acquire.o(.ARM.exidx) refers to cxa_guard_acquire.o(i.__cxa_guard_acquire) for i.__cxa_guard_acquire
    cxa_guard_release.o(.ARM.exidx) refers to cxa_guard_release.o(i.__cxa_guard_release) for i.__cxa_guard_release
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_char_common.o(.text) refers to __printf_wp.o(.text) for __printf
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000031) refers (Weak) to init_aeabi.o(.text) for __cpp_initialize__aeabi_
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing LED_Buzzer.o(.rev16_text), (4 bytes).
    Removing LED_Buzzer.o(.revsh_text), (4 bytes).
    Removing LED_Buzzer.o(.rrx_text), (6 bytes).
    Removing LED_Buzzer.o(i._ZN10LED_Buzzer2onEv), (18 bytes).
    Removing LED_Buzzer.o(i._ZN10LED_Buzzer3offEv), (18 bytes).
    Removing LED_Buzzer.o(i._ZN10LED_Buzzer4initEv), (4 bytes).
    Removing LED_Buzzer.o(i._ZN10LED_Buzzer4turnEv), (8 bytes).
    Removing LED_Buzzer.o(i._ZN10LED_Buzzer8only_oneEj), (32 bytes).
    Removing LED_Buzzer.o(i._ZN10LED_BuzzerC1EP12GPIO_TypeDeft12Enable_level), (8 bytes).
    Removing LED_Buzzer.o(.ARM.exidx), (8 bytes).
    Removing LED_Buzzer.o(.ARM.exidx), (8 bytes).
    Removing LED_Buzzer.o(.ARM.exidx), (8 bytes).
    Removing LED_Buzzer.o(.ARM.exidx), (8 bytes).
    Removing LED_Buzzer.o(.ARM.exidx), (8 bytes).
    Removing LED_Buzzer.o(.ARM.exidx), (8 bytes).
    Removing ZDT.o(.rev16_text), (4 bytes).
    Removing ZDT.o(.revsh_text), (4 bytes).
    Removing ZDT.o(.rrx_text), (6 bytes).
    Removing ZDT.o(i._ZN3ZDT13speed_controlEhthh), (60 bytes).
    Removing ZDT.o(i._ZN3ZDT15synchronizationEv), (16 bytes).
    Removing ZDT.o(.ARM.exidx), (8 bytes).
    Removing ZDT.o(.ARM.exidx), (8 bytes).
    Removing ZDT.o(.ARM.exidx), (8 bytes).
    Removing ZDT.o(.ARM.exidx), (8 bytes).
    Removing ZDT.o(.ARM.exidx), (8 bytes).
    Removing ZDT.o(.ARM.exidx), (8 bytes).
    Removing app_state.o(.rev16_text), (4 bytes).
    Removing app_state.o(.revsh_text), (4 bytes).
    Removing app_state.o(.rrx_text), (6 bytes).
    Removing app_state.o(i._Z7car_runf), (96 bytes).
    Removing app_state.o(i._Z8car_turnf), (96 bytes).
    Removing app_state.o(.ARM.exidx), (8 bytes).
    Removing app_state.o(.ARM.exidx), (8 bytes).
    Removing callback.o(.rev16_text), (4 bytes).
    Removing callback.o(.revsh_text), (4 bytes).
    Removing callback.o(.rrx_text), (6 bytes).
    Removing callback.o(.ARM.exidx), (8 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing font.o(.rev16_text), (4 bytes).
    Removing font.o(.revsh_text), (4 bytes).
    Removing font.o(.rrx_text), (6 bytes).
    Removing font.o(.constdata), (552 bytes).
    Removing font.o(.constdata), (8 bytes).
    Removing font.o(.constdata), (1140 bytes).
    Removing font.o(.constdata), (8 bytes).
    Removing font.o(.constdata), (3420 bytes).
    Removing font.o(.constdata), (8 bytes).
    Removing font.o(.constdata), (306 bytes).
    Removing font.o(.constdata), (8 bytes).
    Removing freertos.o(.rev16_text), (4 bytes).
    Removing freertos.o(.revsh_text), (4 bytes).
    Removing freertos.o(.rrx_text), (6 bytes).
    Removing freertos.o(i.Encoder_Task), (8 bytes).
    Removing freertos.o(i.LED_Task), (8 bytes).
    Removing freertos.o(i.OLED_Task), (8 bytes).
    Removing freertos.o(i.draw_Task), (8 bytes).
    Removing freertos.o(i.main_Task), (8 bytes).
    Removing freertos.o(i.vofa_Task), (8 bytes).
    Removing global_variable.o(.rev16_text), (4 bytes).
    Removing global_variable.o(.revsh_text), (4 bytes).
    Removing global_variable.o(.rrx_text), (6 bytes).
    Removing global_variable.o(.ARM.exidx), (8 bytes).
    Removing global_variable.o(.ARM.exidx), (8 bytes).
    Removing global_variable.o(.data), (1 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (68 bytes).
    Removing jy901s.o(.rev16_text), (4 bytes).
    Removing jy901s.o(.revsh_text), (4 bytes).
    Removing jy901s.o(.rrx_text), (6 bytes).
    Removing jy901s.o(i._ZN6jy901s12reverse_dataEPf), (18 bytes).
    Removing jy901s.o(i._ZN6jy901s6getYawEv), (2 bytes).
    Removing jy901s.o(i._ZN6jy901s7getRollEv), (4 bytes).
    Removing jy901s.o(i._ZN6jy901s8getPitchEv), (4 bytes).
    Removing jy901s.o(.ARM.exidx), (8 bytes).
    Removing jy901s.o(.ARM.exidx), (8 bytes).
    Removing jy901s.o(.ARM.exidx), (8 bytes).
    Removing jy901s.o(.ARM.exidx), (8 bytes).
    Removing jy901s.o(.ARM.exidx), (8 bytes).
    Removing jy901s.o(.ARM.exidx), (8 bytes).
    Removing jy901s.o(.ARM.exidx), (8 bytes).
    Removing jy901s.o(.ARM.exidx), (8 bytes).
    Removing jy901s.o(.ARM.exidx), (8 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing key.o(i._ZN3KEY9detectionEv), (56 bytes).
    Removing key.o(.ARM.exidx), (8 bytes).
    Removing key.o(.ARM.exidx), (8 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(.ARM.exidx), (8 bytes).
    Removing main.o(.ARM.exidx), (8 bytes).
    Removing main.o(.ARM.exidx), (8 bytes).
    Removing main.o(.ARM.exidx), (8 bytes).
    Removing maixcam.o(.rev16_text), (4 bytes).
    Removing maixcam.o(.revsh_text), (4 bytes).
    Removing maixcam.o(.rrx_text), (6 bytes).
    Removing maixcam.o(.ARM.exidx), (8 bytes).
    Removing maixcam.o(.ARM.exidx), (8 bytes).
    Removing maixcam.o(.ARM.exidx), (8 bytes).
    Removing motor.o(.rev16_text), (4 bytes).
    Removing motor.o(.revsh_text), (4 bytes).
    Removing motor.o(.rrx_text), (6 bytes).
    Removing motor.o(i._ZN6TB661212get_duty_maxEv), (6 bytes).
    Removing motor.o(i._ZN6TB661213zero_distanceEv), (16 bytes).
    Removing motor.o(.ARM.exidx), (8 bytes).
    Removing motor.o(.ARM.exidx), (8 bytes).
    Removing motor.o(.ARM.exidx), (8 bytes).
    Removing motor.o(.ARM.exidx), (8 bytes).
    Removing motor.o(.ARM.exidx), (8 bytes).
    Removing motor.o(.ARM.exidx), (8 bytes).
    Removing motor.o(.ARM.exidx), (8 bytes).
    Removing motor.o(.ARM.exidx), (8 bytes).
    Removing my_task.o(.rev16_text), (4 bytes).
    Removing my_task.o(.revsh_text), (4 bytes).
    Removing my_task.o(.rrx_text), (6 bytes).
    Removing my_task.o(.ARM.exidx), (8 bytes).
    Removing my_task.o(.ARM.exidx), (8 bytes).
    Removing my_task.o(.ARM.exidx), (8 bytes).
    Removing my_task.o(.ARM.exidx), (8 bytes).
    Removing my_task.o(.ARM.exidx), (8 bytes).
    Removing my_task.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i._Z12OLED_SetBytehhh14OLED_ColorMode), (28 bytes).
    Removing oled.o(i._Z13OLED_DrawLinehhhh14OLED_ColorMode), (260 bytes).
    Removing oled.o(i._Z13OLED_SetPixelhh14OLED_ColorMode), (48 bytes).
    Removing oled.o(i._Z14OLED_DrawImagehhPK5Image14OLED_ColorMode), (18 bytes).
    Removing oled.o(i._Z15OLED_DisPlay_Onv), (24 bytes).
    Removing oled.o(i._Z15OLED_DrawCirclehhh14OLED_ColorMode), (240 bytes).
    Removing oled.o(i._Z16OLED_DisPlay_Offv), (24 bytes).
    Removing oled.o(i._Z16OLED_DrawEllipsehhhh14OLED_ColorMode), (492 bytes).
    Removing oled.o(i._Z17OLED_DrawTrianglehhhhhh14OLED_ColorMode), (56 bytes).
    Removing oled.o(i._Z17OLED_SetColorMode14OLED_ColorMode), (18 bytes).
    Removing oled.o(i._Z18OLED_DrawRectanglehhhh14OLED_ColorMode), (72 bytes).
    Removing oled.o(i._Z21OLED_DrawFilledCirclehhh14OLED_ColorMode), (174 bytes).
    Removing oled.o(i._Z21OLED_PrintASCIIStringhhPcPK9ASCIIFont14OLED_ColorMode), (46 bytes).
    Removing oled.o(i._Z23OLED_DrawFilledTrianglehhhhhh14OLED_ColorMode), (192 bytes).
    Removing oled.o(i._Z24OLED_DrawFilledRectanglehhhh14OLED_ColorMode), (50 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing oled.o(.ARM.exidx), (8 bytes).
    Removing pid.o(.rev16_text), (4 bytes).
    Removing pid.o(.revsh_text), (4 bytes).
    Removing pid.o(.rrx_text), (6 bytes).
    Removing pid.o(i._ZN14PID_Controller11deinit_dataEv), (32 bytes).
    Removing pid.o(i._ZN14PID_Controller14integral_limitEf), (44 bytes).
    Removing pid.o(.ARM.exidx), (8 bytes).
    Removing pid.o(.ARM.exidx), (8 bytes).
    Removing pid.o(.ARM.exidx), (8 bytes).
    Removing pid.o(.ARM.exidx), (8 bytes).
    Removing pid.o(.ARM.exidx), (8 bytes).
    Removing servo.o(.rev16_text), (4 bytes).
    Removing servo.o(.revsh_text), (4 bytes).
    Removing servo.o(.rrx_text), (6 bytes).
    Removing servo.o(i._ZN5Servo9set_angleEt), (54 bytes).
    Removing servo.o(i._ZN5ServoC1EP17TIM_HandleTypeDefj), (6 bytes).
    Removing servo.o(.ARM.exidx), (8 bytes).
    Removing servo.o(.ARM.exidx), (8 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_timebase_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_timebase_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_timebase_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_timebase_tim.o(i.HAL_ResumeTick), (20 bytes).
    Removing stm32f4xx_hal_timebase_tim.o(i.HAL_SuspendTick), (20 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (120 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (36 bytes).
    Removing tim.o(i.HAL_TIM_Encoder_MspDeInit), (80 bytes).
    Removing tim.o(i.HAL_TIM_PWM_MspDeInit), (28 bytes).
    Removing truck.o(.rev16_text), (4 bytes).
    Removing truck.o(.revsh_text), (4 bytes).
    Removing truck.o(.rrx_text), (6 bytes).
    Removing truck.o(i._ZN5Truck16sampling_sensorsEv), (276 bytes).
    Removing truck.o(.ARM.exidx), (8 bytes).
    Removing truck.o(.ARM.exidx), (8 bytes).
    Removing truck.o(.data), (8 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (212 bytes).
    Removing vofa.o(.rev16_text), (4 bytes).
    Removing vofa.o(.revsh_text), (4 bytes).
    Removing vofa.o(.rrx_text), (6 bytes).
    Removing vofa.o(i._ZN4vofa9debug_pidE14PID_Controller), (192 bytes).
    Removing vofa.o(.ARM.exidx), (8 bytes).
    Removing vofa.o(.ARM.exidx), (8 bytes).
    Removing vofa.o(.ARM.exidx), (8 bytes).
    Removing vofa.o(.ARM.exidx), (8 bytes).
    Removing vofa.o(.ARM.exidx), (8 bytes).
    Removing vofa.o(.bss), (80 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (72 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_InitTick), (64 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config), (40 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (98 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (288 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (82 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (70 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (30 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (14 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (100 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2432 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (108 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (144 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (168 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (104 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (80 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (200 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (124 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (92 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (80 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (152 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (92 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (64 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (204 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (360 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f4xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit), (50 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (68 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (58 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (98 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (496 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (196 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (552 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (320 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (452 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (212 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (184 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (516 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (220 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write), (304 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (404 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (208 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (372 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (124 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (116 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (116 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (124 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAError), (64 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt), (274 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead), (252 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (112 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (88 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (180 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (140 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (104 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (96 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (316 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (308 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (192 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource), (220 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (428 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (292 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (228 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (460 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (268 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (160 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (146 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (230 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig), (20 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage), (34 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage), (36 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (208 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (224 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (28 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop), (112 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (176 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit), (160 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout), (114 bytes).
    Removing cmsis_os2.o(.rev16_text), (4 bytes).
    Removing cmsis_os2.o(.revsh_text), (4 bytes).
    Removing cmsis_os2.o(.rrx_text), (6 bytes).
    Removing cmsis_os2.o(i.AllocBlock), (18 bytes).
    Removing cmsis_os2.o(i.CreateBlock), (26 bytes).
    Removing cmsis_os2.o(i.OS_Tick_GetCount), (12 bytes).
    Removing cmsis_os2.o(i.TimerCallback), (24 bytes).
    Removing cmsis_os2.o(i.osDelayUntil), (46 bytes).
    Removing cmsis_os2.o(i.osEventFlagsClear), (62 bytes).
    Removing cmsis_os2.o(i.osEventFlagsDelete), (32 bytes).
    Removing cmsis_os2.o(i.osEventFlagsGet), (22 bytes).
    Removing cmsis_os2.o(i.osEventFlagsNew), (44 bytes).
    Removing cmsis_os2.o(i.osEventFlagsSet), (84 bytes).
    Removing cmsis_os2.o(i.osEventFlagsWait), (98 bytes).
    Removing cmsis_os2.o(i.osKernelGetInfo), (60 bytes).
    Removing cmsis_os2.o(i.osKernelGetState), (32 bytes).
    Removing cmsis_os2.o(i.osKernelGetSysTimerCount), (66 bytes).
    Removing cmsis_os2.o(i.osKernelGetSysTimerFreq), (12 bytes).
    Removing cmsis_os2.o(i.osKernelGetTickCount), (14 bytes).
    Removing cmsis_os2.o(i.osKernelGetTickFreq), (6 bytes).
    Removing cmsis_os2.o(i.osKernelLock), (42 bytes).
    Removing cmsis_os2.o(i.osKernelRestoreLock), (66 bytes).
    Removing cmsis_os2.o(i.osKernelUnlock), (58 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolAlloc), (144 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolDelete), (96 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolFree), (184 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolGetBlockSize), (24 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolGetCapacity), (24 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolGetCount), (52 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolGetName), (18 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolGetSpace), (40 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolNew), (228 bytes).
    Removing cmsis_os2.o(i.osMessageQueueDelete), (42 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGet), (100 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetCapacity), (8 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetCount), (20 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetMsgSize), (8 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetSpace), (44 bytes).
    Removing cmsis_os2.o(i.osMessageQueueNew), (88 bytes).
    Removing cmsis_os2.o(i.osMessageQueuePut), (104 bytes).
    Removing cmsis_os2.o(i.osMessageQueueReset), (34 bytes).
    Removing cmsis_os2.o(i.osMutexAcquire), (82 bytes).
    Removing cmsis_os2.o(i.osMutexDelete), (44 bytes).
    Removing cmsis_os2.o(i.osMutexGetOwner), (20 bytes).
    Removing cmsis_os2.o(i.osMutexNew), (104 bytes).
    Removing cmsis_os2.o(i.osMutexRelease), (66 bytes).
    Removing cmsis_os2.o(i.osSemaphoreAcquire), (92 bytes).
    Removing cmsis_os2.o(i.osSemaphoreDelete), (42 bytes).
    Removing cmsis_os2.o(i.osSemaphoreGetCount), (20 bytes).
    Removing cmsis_os2.o(i.osSemaphoreNew), (152 bytes).
    Removing cmsis_os2.o(i.osSemaphoreRelease), (88 bytes).
    Removing cmsis_os2.o(i.osThreadEnumerate), (98 bytes).
    Removing cmsis_os2.o(i.osThreadExit), (8 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsClear), (78 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsGet), (44 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsSet), (116 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsWait), (150 bytes).
    Removing cmsis_os2.o(i.osThreadGetCount), (14 bytes).
    Removing cmsis_os2.o(i.osThreadGetId), (4 bytes).
    Removing cmsis_os2.o(i.osThreadGetName), (16 bytes).
    Removing cmsis_os2.o(i.osThreadGetPriority), (18 bytes).
    Removing cmsis_os2.o(i.osThreadGetStackSpace), (22 bytes).
    Removing cmsis_os2.o(i.osThreadGetState), (54 bytes).
    Removing cmsis_os2.o(i.osThreadResume), (32 bytes).
    Removing cmsis_os2.o(i.osThreadSetPriority), (40 bytes).
    Removing cmsis_os2.o(i.osThreadSuspend), (32 bytes).
    Removing cmsis_os2.o(i.osThreadTerminate), (52 bytes).
    Removing cmsis_os2.o(i.osThreadYield), (36 bytes).
    Removing cmsis_os2.o(i.osTimerDelete), (68 bytes).
    Removing cmsis_os2.o(i.osTimerGetName), (16 bytes).
    Removing cmsis_os2.o(i.osTimerIsRunning), (16 bytes).
    Removing cmsis_os2.o(i.osTimerNew), (120 bytes).
    Removing cmsis_os2.o(i.osTimerStart), (50 bytes).
    Removing cmsis_os2.o(i.osTimerStop), (68 bytes).
    Removing event_groups.o(i.prvTestWaitCondition), (20 bytes).
    Removing event_groups.o(i.uxEventGroupGetNumber), (8 bytes).
    Removing event_groups.o(i.vEventGroupClearBitsCallback), (4 bytes).
    Removing event_groups.o(i.vEventGroupDelete), (74 bytes).
    Removing event_groups.o(i.vEventGroupSetBitsCallback), (4 bytes).
    Removing event_groups.o(i.vEventGroupSetNumber), (4 bytes).
    Removing event_groups.o(i.xEventGroupClearBits), (64 bytes).
    Removing event_groups.o(i.xEventGroupClearBitsFromISR), (16 bytes).
    Removing event_groups.o(i.xEventGroupCreate), (28 bytes).
    Removing event_groups.o(i.xEventGroupCreateStatic), (44 bytes).
    Removing event_groups.o(i.xEventGroupGetBitsFromISR), (26 bytes).
    Removing event_groups.o(i.xEventGroupSetBits), (140 bytes).
    Removing event_groups.o(i.xEventGroupSetBitsFromISR), (16 bytes).
    Removing event_groups.o(i.xEventGroupSync), (204 bytes).
    Removing event_groups.o(i.xEventGroupWaitBits), (264 bytes).
    Removing heap_4.o(i.vPortGetHeapStats), (108 bytes).
    Removing heap_4.o(i.vPortInitialiseBlocks), (2 bytes).
    Removing heap_4.o(i.xPortGetFreeHeapSize), (12 bytes).
    Removing heap_4.o(i.xPortGetMinimumEverFreeHeapSize), (12 bytes).
    Removing port.o(i.vPortEndScheduler), (32 bytes).
    Removing queue.o(i.pcQueueGetName), (40 bytes).
    Removing queue.o(i.prvInitialiseMutex), (22 bytes).
    Removing queue.o(i.ucQueueGetQueueType), (6 bytes).
    Removing queue.o(i.uxQueueGetQueueNumber), (4 bytes).
    Removing queue.o(i.uxQueueMessagesWaiting), (36 bytes).
    Removing queue.o(i.uxQueueMessagesWaitingFromISR), (22 bytes).
    Removing queue.o(i.uxQueueSpacesAvailable), (40 bytes).
    Removing queue.o(i.vQueueDelete), (46 bytes).
    Removing queue.o(i.vQueueSetQueueNumber), (4 bytes).
    Removing queue.o(i.vQueueUnregisterQueue), (40 bytes).
    Removing queue.o(i.xQueueCreateCountingSemaphore), (58 bytes).
    Removing queue.o(i.xQueueCreateCountingSemaphoreStatic), (64 bytes).
    Removing queue.o(i.xQueueCreateMutex), (22 bytes).
    Removing queue.o(i.xQueueCreateMutexStatic), (26 bytes).
    Removing queue.o(i.xQueueGenericCreate), (66 bytes).
    Removing queue.o(i.xQueueGetMutexHolder), (28 bytes).
    Removing queue.o(i.xQueueGetMutexHolderFromISR), (30 bytes).
    Removing queue.o(i.xQueueGiveFromISR), (156 bytes).
    Removing queue.o(i.xQueueGiveMutexRecursive), (62 bytes).
    Removing queue.o(i.xQueueIsQueueEmptyFromISR), (30 bytes).
    Removing queue.o(i.xQueueIsQueueFullFromISR), (34 bytes).
    Removing queue.o(i.xQueuePeek), (308 bytes).
    Removing queue.o(i.xQueuePeekFromISR), (116 bytes).
    Removing queue.o(i.xQueueReceiveFromISR), (154 bytes).
    Removing queue.o(i.xQueueSemaphoreTake), (376 bytes).
    Removing queue.o(i.xQueueTakeMutexRecursive), (64 bytes).
    Removing stream_buffer.o(i.prvBytesInBuffer), (18 bytes).
    Removing stream_buffer.o(i.prvInitialiseNewStreamBuffer), (66 bytes).
    Removing stream_buffer.o(i.prvReadBytesFromBuffer), (140 bytes).
    Removing stream_buffer.o(i.prvReadMessageFromBuffer), (58 bytes).
    Removing stream_buffer.o(i.prvWriteBytesToBuffer), (130 bytes).
    Removing stream_buffer.o(i.prvWriteMessageToBuffer), (60 bytes).
    Removing stream_buffer.o(i.ucStreamBufferGetStreamBufferType), (8 bytes).
    Removing stream_buffer.o(i.uxStreamBufferGetStreamBufferNumber), (4 bytes).
    Removing stream_buffer.o(i.vStreamBufferDelete), (34 bytes).
    Removing stream_buffer.o(i.vStreamBufferSetStreamBufferNumber), (4 bytes).
    Removing stream_buffer.o(i.xStreamBufferBytesAvailable), (22 bytes).
    Removing stream_buffer.o(i.xStreamBufferGenericCreate), (108 bytes).
    Removing stream_buffer.o(i.xStreamBufferGenericCreateStatic), (122 bytes).
    Removing stream_buffer.o(i.xStreamBufferIsEmpty), (34 bytes).
    Removing stream_buffer.o(i.xStreamBufferIsFull), (48 bytes).
    Removing stream_buffer.o(i.xStreamBufferNextMessageLengthBytes), (78 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceive), (212 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceiveCompletedFromISR), (70 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceiveFromISR), (144 bytes).
    Removing stream_buffer.o(i.xStreamBufferReset), (70 bytes).
    Removing stream_buffer.o(i.xStreamBufferSend), (258 bytes).
    Removing stream_buffer.o(i.xStreamBufferSendCompletedFromISR), (70 bytes).
    Removing stream_buffer.o(i.xStreamBufferSendFromISR), (142 bytes).
    Removing stream_buffer.o(i.xStreamBufferSetTriggerLevel), (40 bytes).
    Removing stream_buffer.o(i.xStreamBufferSpacesAvailable), (38 bytes).
    Removing tasks.o(i.eTaskGetState), (116 bytes).
    Removing tasks.o(i.pcTaskGetName), (32 bytes).
    Removing tasks.o(i.prvListTasksWithinSingleList), (88 bytes).
    Removing tasks.o(i.prvTaskCheckFreeStackSpace), (20 bytes).
    Removing tasks.o(i.prvTaskIsTaskSuspended), (52 bytes).
    Removing tasks.o(i.pvTaskIncrementMutexHeldCount), (24 bytes).
    Removing tasks.o(i.ulTaskNotifyTake), (104 bytes).
    Removing tasks.o(i.ulTaskNotifyValueClear), (44 bytes).
    Removing tasks.o(i.uxTaskGetNumberOfTasks), (12 bytes).
    Removing tasks.o(i.uxTaskGetStackHighWaterMark), (16 bytes).
    Removing tasks.o(i.uxTaskGetSystemState), (172 bytes).
    Removing tasks.o(i.uxTaskGetTaskNumber), (8 bytes).
    Removing tasks.o(i.uxTaskPriorityGet), (28 bytes).
    Removing tasks.o(i.uxTaskPriorityGetFromISR), (44 bytes).
    Removing tasks.o(i.uxTaskResetEventItemValue), (24 bytes).
    Removing tasks.o(i.vTaskDelayUntil), (140 bytes).
    Removing tasks.o(i.vTaskDelete), (144 bytes).
    Removing tasks.o(i.vTaskEndScheduler), (28 bytes).
    Removing tasks.o(i.vTaskGetInfo), (116 bytes).
    Removing tasks.o(i.vTaskNotifyGiveFromISR), (176 bytes).
    Removing tasks.o(i.vTaskPlaceOnUnorderedEventList), (80 bytes).
    Removing tasks.o(i.vTaskPriorityDisinheritAfterTimeout), (144 bytes).
    Removing tasks.o(i.vTaskPrioritySet), (180 bytes).
    Removing tasks.o(i.vTaskRemoveFromUnorderedEventList), (112 bytes).
    Removing tasks.o(i.vTaskResume), (124 bytes).
    Removing tasks.o(i.vTaskSetTaskNumber), (8 bytes).
    Removing tasks.o(i.vTaskSetTimeOutState), (48 bytes).
    Removing tasks.o(i.vTaskSuspend), (156 bytes).
    Removing tasks.o(i.xTaskCatchUpTicks), (48 bytes).
    Removing tasks.o(i.xTaskGenericNotify), (224 bytes).
    Removing tasks.o(i.xTaskGenericNotifyFromISR), (260 bytes).
    Removing tasks.o(i.xTaskGetCurrentTaskHandle), (12 bytes).
    Removing tasks.o(i.xTaskGetTickCountFromISR), (16 bytes).
    Removing tasks.o(i.xTaskNotifyStateClear), (52 bytes).
    Removing tasks.o(i.xTaskNotifyWait), (140 bytes).
    Removing tasks.o(i.xTaskPriorityInherit), (120 bytes).
    Removing tasks.o(i.xTaskResumeFromISR), (136 bytes).
    Removing timers.o(i.pcTimerGetName), (22 bytes).
    Removing timers.o(i.prvInitialiseNewTimer), (78 bytes).
    Removing timers.o(i.pvTimerGetTimerID), (36 bytes).
    Removing timers.o(i.uxTimerGetReloadMode), (48 bytes).
    Removing timers.o(i.uxTimerGetTimerNumber), (4 bytes).
    Removing timers.o(i.vTimerSetReloadMode), (54 bytes).
    Removing timers.o(i.vTimerSetTimerID), (38 bytes).
    Removing timers.o(i.vTimerSetTimerNumber), (4 bytes).
    Removing timers.o(i.xTimerCreate), (52 bytes).
    Removing timers.o(i.xTimerCreateStatic), (46 bytes).
    Removing timers.o(i.xTimerGetExpiryTime), (22 bytes).
    Removing timers.o(i.xTimerGetPeriod), (22 bytes).
    Removing timers.o(i.xTimerGetTimerDaemonTaskHandle), (32 bytes).
    Removing timers.o(i.xTimerIsTimerActive), (48 bytes).
    Removing timers.o(i.xTimerPendFunctionCall), (60 bytes).
    Removing timers.o(i.xTimerPendFunctionCallFromISR), (40 bytes).

831 unused section(s) (total 58993 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../../../edgfe/lib_src/guard.c           0x00000000   Number         0  cxa_guard_release.o ABSOLUTE
    ../../../edgfe/lib_src/guard.c           0x00000000   Number         0  cxa_guard_acquire.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/arm_runtime.c                    0x00000000   Number         0  init_aeabi.o ABSOLUTE
    ../clib/arm_runtime.c                    0x00000000   Number         0  init_aeabi.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  memset.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  aeabi_memset.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    .\..\Core\Src\LED_Buzzer.cpp             0x00000000   Number         0  LED_Buzzer.o ABSOLUTE
    .\..\Core\Src\ZDT.cpp                    0x00000000   Number         0  ZDT.o ABSOLUTE
    .\..\Core\Src\app_state.cpp              0x00000000   Number         0  app_state.o ABSOLUTE
    .\..\Core\Src\callback.cpp               0x00000000   Number         0  callback.o ABSOLUTE
    .\..\Core\Src\dma.c                      0x00000000   Number         0  dma.o ABSOLUTE
    .\..\Core\Src\font.cpp                   0x00000000   Number         0  font.o ABSOLUTE
    .\..\Core\Src\freertos.c                 0x00000000   Number         0  freertos.o ABSOLUTE
    .\..\Core\Src\global_variable.cpp        0x00000000   Number         0  global_variable.o ABSOLUTE
    .\..\Core\Src\gpio.c                     0x00000000   Number         0  gpio.o ABSOLUTE
    .\..\Core\Src\i2c.c                      0x00000000   Number         0  i2c.o ABSOLUTE
    .\..\Core\Src\jy901s.cpp                 0x00000000   Number         0  jy901s.o ABSOLUTE
    .\..\Core\Src\key.cpp                    0x00000000   Number         0  key.o ABSOLUTE
    .\..\Core\Src\main.cpp                   0x00000000   Number         0  main.o ABSOLUTE
    .\..\Core\Src\maixcam.cpp                0x00000000   Number         0  maixcam.o ABSOLUTE
    .\..\Core\Src\motor.cpp                  0x00000000   Number         0  motor.o ABSOLUTE
    .\..\Core\Src\my_task.cpp                0x00000000   Number         0  my_task.o ABSOLUTE
    .\..\Core\Src\oled.cpp                   0x00000000   Number         0  oled.o ABSOLUTE
    .\..\Core\Src\pid.cpp                    0x00000000   Number         0  pid.o ABSOLUTE
    .\..\Core\Src\servo.cpp                  0x00000000   Number         0  servo.o ABSOLUTE
    .\..\Core\Src\stm32f4xx_hal_msp.c        0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    .\..\Core\Src\stm32f4xx_hal_timebase_tim.c 0x00000000   Number         0  stm32f4xx_hal_timebase_tim.o ABSOLUTE
    .\..\Core\Src\stm32f4xx_it.c             0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    .\..\Core\Src\system_stm32f4xx.c         0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    .\..\Core\Src\tim.c                      0x00000000   Number         0  tim.o ABSOLUTE
    .\..\Core\Src\truck.cpp                  0x00000000   Number         0  truck.o ABSOLUTE
    .\..\Core\Src\usart.c                    0x00000000   Number         0  usart.o ABSOLUTE
    .\..\Core\Src\vofa.cpp                   0x00000000   Number         0  vofa.o ABSOLUTE
    .\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    .\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    .\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    .\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    .\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    .\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    .\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    .\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    .\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    .\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    .\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    .\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    .\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    .\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    .\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    .\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    .\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    .\..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    .\..\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.c 0x00000000   Number         0  cmsis_os2.o ABSOLUTE
    .\..\Middlewares\Third_Party\FreeRTOS\Source\croutine.c 0x00000000   Number         0  croutine.o ABSOLUTE
    .\..\Middlewares\Third_Party\FreeRTOS\Source\event_groups.c 0x00000000   Number         0  event_groups.o ABSOLUTE
    .\..\Middlewares\Third_Party\FreeRTOS\Source\list.c 0x00000000   Number         0  list.o ABSOLUTE
    .\..\Middlewares\Third_Party\FreeRTOS\Source\portable\MemMang\heap_4.c 0x00000000   Number         0  heap_4.o ABSOLUTE
    .\..\Middlewares\Third_Party\FreeRTOS\Source\portable\RVDS\ARM_CM4F\port.c 0x00000000   Number         0  port.o ABSOLUTE
    .\..\Middlewares\Third_Party\FreeRTOS\Source\queue.c 0x00000000   Number         0  queue.o ABSOLUTE
    .\..\Middlewares\Third_Party\FreeRTOS\Source\stream_buffer.c 0x00000000   Number         0  stream_buffer.o ABSOLUTE
    .\..\Middlewares\Third_Party\FreeRTOS\Source\tasks.c 0x00000000   Number         0  tasks.o ABSOLUTE
    .\..\Middlewares\Third_Party\FreeRTOS\Source\timers.c 0x00000000   Number         0  timers.o ABSOLUTE
    .\\..\\Core\\Src\\LED_Buzzer.cpp         0x00000000   Number         0  LED_Buzzer.o ABSOLUTE
    .\\..\\Core\\Src\\ZDT.cpp                0x00000000   Number         0  ZDT.o ABSOLUTE
    .\\..\\Core\\Src\\app_state.cpp          0x00000000   Number         0  app_state.o ABSOLUTE
    .\\..\\Core\\Src\\callback.cpp           0x00000000   Number         0  callback.o ABSOLUTE
    .\\..\\Core\\Src\\dma.c                  0x00000000   Number         0  dma.o ABSOLUTE
    .\\..\\Core\\Src\\font.cpp               0x00000000   Number         0  font.o ABSOLUTE
    .\\..\\Core\\Src\\freertos.c             0x00000000   Number         0  freertos.o ABSOLUTE
    .\\..\\Core\\Src\\global_variable.cpp    0x00000000   Number         0  global_variable.o ABSOLUTE
    .\\..\\Core\\Src\\gpio.c                 0x00000000   Number         0  gpio.o ABSOLUTE
    .\\..\\Core\\Src\\i2c.c                  0x00000000   Number         0  i2c.o ABSOLUTE
    .\\..\\Core\\Src\\jy901s.cpp             0x00000000   Number         0  jy901s.o ABSOLUTE
    .\\..\\Core\\Src\\key.cpp                0x00000000   Number         0  key.o ABSOLUTE
    .\\..\\Core\\Src\\main.cpp               0x00000000   Number         0  main.o ABSOLUTE
    .\\..\\Core\\Src\\maixcam.cpp            0x00000000   Number         0  maixcam.o ABSOLUTE
    .\\..\\Core\\Src\\motor.cpp              0x00000000   Number         0  motor.o ABSOLUTE
    .\\..\\Core\\Src\\my_task.cpp            0x00000000   Number         0  my_task.o ABSOLUTE
    .\\..\\Core\\Src\\oled.cpp               0x00000000   Number         0  oled.o ABSOLUTE
    .\\..\\Core\\Src\\pid.cpp                0x00000000   Number         0  pid.o ABSOLUTE
    .\\..\\Core\\Src\\servo.cpp              0x00000000   Number         0  servo.o ABSOLUTE
    .\\..\\Core\\Src\\stm32f4xx_hal_msp.c    0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    .\\..\\Core\\Src\\stm32f4xx_hal_timebase_tim.c 0x00000000   Number         0  stm32f4xx_hal_timebase_tim.o ABSOLUTE
    .\\..\\Core\\Src\\stm32f4xx_it.c         0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    .\\..\\Core\\Src\\system_stm32f4xx.c     0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    .\\..\\Core\\Src\\tim.c                  0x00000000   Number         0  tim.o ABSOLUTE
    .\\..\\Core\\Src\\truck.cpp              0x00000000   Number         0  truck.o ABSOLUTE
    .\\..\\Core\\Src\\usart.c                0x00000000   Number         0  usart.o ABSOLUTE
    .\\..\\Core\\Src\\vofa.cpp               0x00000000   Number         0  vofa.o ABSOLUTE
    .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    .\\..\\Middlewares\\Third_Party\\FreeRTOS\\Source\\CMSIS_RTOS_V2\\cmsis_os2.c 0x00000000   Number         0  cmsis_os2.o ABSOLUTE
    .\\..\\Middlewares\\Third_Party\\FreeRTOS\\Source\\portable\\RVDS\\ARM_CM4F\\port.c 0x00000000   Number         0  port.o ABSOLUTE
    .\startup_stm32f407xx.s                  0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001fc   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x080001fc   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000009  0x08000202   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$00000017  0x08000208   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x0800020c   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800020e   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000212   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000212   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000212   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000212   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000212   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000218   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000218   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000218   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000218   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x08000222   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000222   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000222   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000222   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000222   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000222   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000222   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000222   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000222   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000222   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000222   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000031          0x08000222   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000031)
    .ARM.Collect$$libinit$$00000032          0x08000226   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000226   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000228   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0800022a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0800022a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x0800022a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x0800022a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x0800022a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x0800022a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x0800022a   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x0800022c   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0800022c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0800022c   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000232   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000232   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000236   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000236   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800023e   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000240   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000240   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000244   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .emb_text                                0x0800024c   Section      190  port.o(.emb_text)
    $v0                                      0x0800024c   Number         0  port.o(.emb_text)
    .text                                    0x0800030c   Section       64  startup_stm32f407xx.o(.text)
    $v0                                      0x0800030c   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x0800034c   Section      238  lludivv7m.o(.text)
    .text                                    0x0800043c   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x08000464   Section        0  _printf_dec.o(.text)
    .text                                    0x080004dc   Section        0  __printf_wp.o(.text)
    .text                                    0x080005ea   Section        0  memcmp.o(.text)
    .text                                    0x08000642   Section        0  strlen.o(.text)
    .text                                    0x08000680   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x0800070a   Section       16  aeabi_memset.o(.text)
    .text                                    0x0800071a   Section       68  rt_memclr.o(.text)
    .text                                    0x0800075e   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080007ac   Section        0  heapauxi.o(.text)
    .text                                    0x080007b4   Section        0  init_aeabi.o(.text)
    .text                                    0x080007d8   Section        0  _printf_intcommon.o(.text)
    .text                                    0x0800088a   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x0800088d   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08000ca8   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000ca9   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000cd8   Section        0  _sputc.o(.text)
    .text                                    0x08000ce2   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000d48   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08000d50   Section      138  lludiv10.o(.text)
    .text                                    0x08000ddc   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08000e5c   Section        0  bigflt0.o(.text)
    .text                                    0x08000f40   Section        8  libspace.o(.text)
    .text                                    0x08000f48   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000f92   Section        0  exit.o(.text)
    .text                                    0x08000fa4   Section      128  strcmpv7m.o(.text)
    .text                                    0x08001024   Section        0  sys_exit.o(.text)
    .text                                    0x08001030   Section        2  use_no_semi.o(.text)
    .text                                    0x08001032   Section        0  indicate_semi.o(.text)
    CL$$btod_d2e                             0x08001032   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08001070   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x080010b6   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08001116   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x0800144e   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x0800152a   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08001554   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x0800157e   Section      580  btod.o(CL$$btod_mult_common)
    i.BusFault_Handler                       0x080017c2   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DMA1_Stream1_IRQHandler                0x080017c4   Section        0  stm32f4xx_it.o(i.DMA1_Stream1_IRQHandler)
    i.DMA1_Stream3_IRQHandler                0x080017d0   Section        0  stm32f4xx_it.o(i.DMA1_Stream3_IRQHandler)
    i.DMA1_Stream5_IRQHandler                0x080017dc   Section        0  stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler)
    i.DMA1_Stream6_IRQHandler                0x080017e8   Section        0  stm32f4xx_it.o(i.DMA1_Stream6_IRQHandler)
    i.DMA2_Stream1_IRQHandler                0x080017f4   Section        0  stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler)
    i.DMA2_Stream2_IRQHandler                0x08001800   Section        0  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    i.DMA2_Stream6_IRQHandler                0x0800180c   Section        0  stm32f4xx_it.o(i.DMA2_Stream6_IRQHandler)
    i.DMA2_Stream7_IRQHandler                0x08001818   Section        0  stm32f4xx_it.o(i.DMA2_Stream7_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x08001824   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x08001825   Thumb Code    34  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x0800184c   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x0800184d   Thumb Code    84  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x080018a0   Section        0  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x080018a1   Thumb Code    40  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x080018c8   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Encoder_Task                           0x080018cc   Section        0  my_task.o(i.Encoder_Task)
    i.Error_Handler                          0x08001918   Section        0  main.o(i.Error_Handler)
    i.HAL_DMA_Abort                          0x0800191c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x080019ae   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_GetState                       0x080019d2   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_GetState)
    i.HAL_DMA_IRQHandler                     0x080019d8   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08001b78   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x08001c4c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x08001cbc   Section        0  stm32f4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x08001ce0   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_TogglePin                     0x08001ed0   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    i.HAL_GPIO_WritePin                      0x08001ee0   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08001eec   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_I2C_AbortCpltCallback              0x08001ef8   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback)
    i.HAL_I2C_AddrCallback                   0x08001efa   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback)
    i.HAL_I2C_ER_IRQHandler                  0x08001efc   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler)
    i.HAL_I2C_EV_IRQHandler                  0x08001fb6   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler)
    i.HAL_I2C_ErrorCallback                  0x080021e6   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback)
    i.HAL_I2C_Init                           0x080021e8   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_ListenCpltCallback             0x08002370   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback)
    i.HAL_I2C_MasterRxCpltCallback           0x08002372   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback)
    i.HAL_I2C_MasterTxCpltCallback           0x08002374   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback)
    i.HAL_I2C_Master_Transmit                0x08002378   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    i.HAL_I2C_MemRxCpltCallback              0x080024a4   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback)
    i.HAL_I2C_MemTxCpltCallback              0x080024a6   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback)
    i.HAL_I2C_MspInit                        0x080024a8   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_I2C_SlaveRxCpltCallback            0x08002530   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback)
    i.HAL_I2C_SlaveTxCpltCallback            0x08002532   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback)
    i.HAL_IncTick                            0x08002534   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08002544   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08002578   Section        0  stm32f4xx_hal_timebase_tim.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08002610   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08002648   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08002664   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x080026a4   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x080026c8   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetClockConfig                 0x080027fc   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x0800283c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x0800285c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x0800287c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x080028dc   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_TIMEx_BreakCallback                0x08002c48   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x08002c4a   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_ConfigBreakDeadTime          0x08002c4c   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08002ca0   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x08002d30   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08002d8c   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x08002dc8   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_Encoder_Init                   0x08002e48   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    i.HAL_TIM_Encoder_MspInit                0x08002eec   Section        0  tim.o(i.HAL_TIM_Encoder_MspInit)
    i.HAL_TIM_Encoder_Start                  0x08002fb4   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    i.HAL_TIM_IC_CaptureCallback             0x08003042   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x08003044   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_MspPostInit                    0x08003174   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_OC_DelayElapsedCallback        0x080031f0   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_ConfigChannel              0x080031f2   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x080032be   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x08003318   Section        0  tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x08003340   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PWM_Start                      0x08003344   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_TIM_PeriodElapsedCallback          0x0800340c   Section        0  main.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x08003434   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x08003436   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x08003484   Section        0  callback.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_ErrorCallback                 0x080035d4   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x080035d8   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08003848   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x080038ac   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_RxCpltCallback                0x08003c20   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x08003c22   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit_DMA                  0x08003c24   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA)
    i.HAL_UART_TxCpltCallback                0x08003c9c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HAL_UART_TxHalfCpltCallback            0x08003c9e   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback)
    i.HardFault_Handler                      0x08003ca0   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.I2C1_ER_IRQHandler                     0x08003ca4   Section        0  stm32f4xx_it.o(i.I2C1_ER_IRQHandler)
    i.I2C1_EV_IRQHandler                     0x08003cb0   Section        0  stm32f4xx_it.o(i.I2C1_EV_IRQHandler)
    i.I2C_DMAAbort                           0x08003cbc   Section        0  stm32f4xx_hal_i2c.o(i.I2C_DMAAbort)
    I2C_DMAAbort                             0x08003cbd   Thumb Code   182  stm32f4xx_hal_i2c.o(i.I2C_DMAAbort)
    i.I2C_Flush_DR                           0x08003d78   Section        0  stm32f4xx_hal_i2c.o(i.I2C_Flush_DR)
    I2C_Flush_DR                             0x08003d79   Thumb Code    16  stm32f4xx_hal_i2c.o(i.I2C_Flush_DR)
    i.I2C_ITError                            0x08003d88   Section        0  stm32f4xx_hal_i2c.o(i.I2C_ITError)
    I2C_ITError                              0x08003d89   Thumb Code   336  stm32f4xx_hal_i2c.o(i.I2C_ITError)
    i.I2C_IsAcknowledgeFailed                0x08003ee0   Section        0  stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    I2C_IsAcknowledgeFailed                  0x08003ee1   Thumb Code    46  stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    i.I2C_MasterReceive_BTF                  0x08003f0e   Section        0  stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF)
    I2C_MasterReceive_BTF                    0x08003f0f   Thumb Code   218  stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF)
    i.I2C_MasterReceive_RXNE                 0x08003fe8   Section        0  stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE)
    I2C_MasterReceive_RXNE                   0x08003fe9   Thumb Code   238  stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE)
    i.I2C_MasterRequestWrite                 0x080040dc   Section        0  stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite)
    I2C_MasterRequestWrite                   0x080040dd   Thumb Code   150  stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite)
    i.I2C_MasterTransmit_BTF                 0x08004178   Section        0  stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF)
    I2C_MasterTransmit_BTF                   0x08004179   Thumb Code   130  stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF)
    i.I2C_MasterTransmit_TXE                 0x080041fc   Section        0  stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE)
    I2C_MasterTransmit_TXE                   0x080041fd   Thumb Code   182  stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE)
    i.I2C_Master_ADDR                        0x080042b4   Section        0  stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR)
    I2C_Master_ADDR                          0x080042b5   Thumb Code   276  stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR)
    i.I2C_Master_SB                          0x080043cc   Section        0  stm32f4xx_hal_i2c.o(i.I2C_Master_SB)
    I2C_Master_SB                            0x080043cd   Thumb Code   140  stm32f4xx_hal_i2c.o(i.I2C_Master_SB)
    i.I2C_MemoryTransmit_TXE_BTF             0x08004458   Section        0  stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF)
    I2C_MemoryTransmit_TXE_BTF               0x08004459   Thumb Code   168  stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF)
    i.I2C_Slave_ADDR                         0x08004500   Section        0  stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR)
    I2C_Slave_ADDR                           0x08004501   Thumb Code    70  stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR)
    i.I2C_Slave_AF                           0x08004548   Section        0  stm32f4xx_hal_i2c.o(i.I2C_Slave_AF)
    I2C_Slave_AF                             0x08004549   Thumb Code   138  stm32f4xx_hal_i2c.o(i.I2C_Slave_AF)
    i.I2C_Slave_STOPF                        0x080045d8   Section        0  stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF)
    I2C_Slave_STOPF                          0x080045d9   Thumb Code   338  stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF)
    i.I2C_WaitOnBTFFlagUntilTimeout          0x08004734   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    I2C_WaitOnBTFFlagUntilTimeout            0x08004735   Thumb Code    86  stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    i.I2C_WaitOnFlagUntilTimeout             0x0800478c   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x0800478d   Thumb Code   144  stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnMasterAddressFlagUntilTimeout 0x0800481c   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    I2C_WaitOnMasterAddressFlagUntilTimeout  0x0800481d   Thumb Code   188  stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    i.I2C_WaitOnTXEFlagUntilTimeout          0x080048d8   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    I2C_WaitOnTXEFlagUntilTimeout            0x080048d9   Thumb Code    86  stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    i.LED_Task                               0x08004930   Section        0  my_task.o(i.LED_Task)
    i.MX_DMA_Init                            0x08004950   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_FREERTOS_Init                       0x080049fc   Section        0  freertos.o(i.MX_FREERTOS_Init)
    i.MX_GPIO_Init                           0x08004a88   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x08004bcc   Section        0  i2c.o(i.MX_I2C1_Init)
    i.MX_TIM1_Init                           0x08004c0c   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM2_Init                           0x08004cbc   Section        0  tim.o(i.MX_TIM2_Init)
    i.MX_TIM3_Init                           0x08004d24   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM6_Init                           0x08004d90   Section        0  tim.o(i.MX_TIM6_Init)
    i.MX_USART1_UART_Init                    0x08004dd4   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x08004e0c   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MX_USART3_UART_Init                    0x08004e44   Section        0  usart.o(i.MX_USART3_UART_Init)
    i.MX_USART6_UART_Init                    0x08004e7c   Section        0  usart.o(i.MX_USART6_UART_Init)
    i.MemManage_Handler                      0x08004eb4   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08004eb6   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.OLED_Task                              0x08004eb8   Section        0  my_task.o(i.OLED_Task)
    i.StartDefaultTask                       0x08004f08   Section        0  freertos.o(i.StartDefaultTask)
    i.SysTick_Handler                        0x08004f10   Section        0  cmsis_os2.o(i.SysTick_Handler)
    i.SystemInit                             0x08004f2c   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM6_DAC_IRQHandler                    0x08004f3c   Section        0  stm32f4xx_it.o(i.TIM6_DAC_IRQHandler)
    i.TIM8_TRG_COM_TIM14_IRQHandler          0x08004f48   Section        0  stm32f4xx_it.o(i.TIM8_TRG_COM_TIM14_IRQHandler)
    i.TIM_Base_SetConfig                     0x08004f54   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x0800502c   Section        0  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_OC1_SetConfig                      0x08005048   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x08005049   Thumb Code    88  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x080050a8   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x08005114   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x08005115   Thumb Code    96  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x0800517c   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x0800517d   Thumb Code    70  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.UART_DMAAbortOnError                   0x080051cc   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x080051cd   Thumb Code    16  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x080051dc   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x080051dd   Thumb Code    74  stm32f4xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x08005226   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x08005227   Thumb Code   134  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x080052ac   Section        0  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x080052ad   Thumb Code    30  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_DMATransmitCplt                   0x080052ca   Section        0  stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt)
    UART_DMATransmitCplt                     0x080052cb   Thumb Code    66  stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt)
    i.UART_DMATxHalfCplt                     0x0800530c   Section        0  stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt)
    UART_DMATxHalfCplt                       0x0800530d   Thumb Code    10  stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt)
    i.UART_EndRxTransfer                     0x08005316   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08005317   Thumb Code    78  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTxTransfer                     0x08005364   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x08005365   Thumb Code    28  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x08005380   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08005381   Thumb Code   194  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08005444   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08005445   Thumb Code   258  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x08005550   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.USART1_IRQHandler                      0x080055e0   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x080055ec   Section        0  stm32f4xx_it.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x080055f8   Section        0  stm32f4xx_it.o(i.USART3_IRQHandler)
    i.USART6_IRQHandler                      0x08005604   Section        0  stm32f4xx_it.o(i.USART6_IRQHandler)
    i.UsageFault_Handler                     0x08005610   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i._Z11system_initv                       0x08005614   Section        0  global_variable.o(i._Z11system_initv)
    i._Z12OLED_SendCmdh                      0x080056a4   Section        0  oled.o(i._Z12OLED_SendCmdh)
    i._Z12OLED_SetBitshhh14OLED_ColorMode    0x080056b4   Section        0  oled.o(i._Z12OLED_SetBitshhh14OLED_ColorMode)
    i._Z13OLED_NewFramev                     0x080056fc   Section        0  oled.o(i._Z13OLED_NewFramev)
    i._Z13OLED_SetBlockhhPKhhh14OLED_ColorMode 0x0800570c   Section        0  oled.o(i._Z13OLED_SetBlockhhPKhhh14OLED_ColorMode)
    i._Z14OLED_ShowFramev                    0x08005794   Section        0  oled.o(i._Z14OLED_ShowFramev)
    i._Z16OLED_PrintStringhhPcPK4Font14OLED_ColorMode 0x080057dc   Section        0  oled.o(i._Z16OLED_PrintStringhhPcPK4Font14OLED_ColorMode)
    i._Z16_OLED_GetUTF8LenPc                 0x08005894   Section        0  oled.o(i._Z16_OLED_GetUTF8LenPc)
    i._Z17OLED_SetBits_Finehhhh14OLED_ColorMode 0x080058c6   Section        0  oled.o(i._Z17OLED_SetBits_Finehhhh14OLED_ColorMode)
    i._Z17OLED_SetByte_Finehhhhh14OLED_ColorMode 0x08005930   Section        0  oled.o(i._Z17OLED_SetByte_Finehhhhh14OLED_ColorMode)
    i._Z18SystemClock_Configv                0x08005978   Section        0  main.o(i._Z18SystemClock_Configv)
    i._Z19OLED_PrintASCIICharhhcPK9ASCIIFont14OLED_ColorMode 0x08005a0c   Section        0  oled.o(i._Z19OLED_PrintASCIICharhhcPK9ASCIIFont14OLED_ColorMode)
    i._Z9OLED_Initv                          0x08005a2e   Section        0  oled.o(i._Z9OLED_Initv)
    i._Z9OLED_SendPhh                        0x08005ae4   Section        0  oled.o(i._Z9OLED_SendPhh)
    i._ZN14PID_Controller10pid_figureEv      0x08005b00   Section        0  pid.o(i._ZN14PID_Controller10pid_figureEv)
    i._ZN14PID_Controller9dead_zoneEv        0x08005bf0   Section        0  pid.o(i._ZN14PID_Controller9dead_zoneEv)
    i._ZN14PID_ControllerC1Efff8pid_modePfS1_S1_fbf 0x08005c2c   Section        0  pid.o(i._ZN14PID_ControllerC1Efff8pid_modePfS1_S1_fbf)
    i._ZN3KEYC1EP12GPIO_TypeDeft8KEY_Mode    0x08005c6c   Section        0  key.o(i._ZN3KEYC1EP12GPIO_TypeDeft8KEY_Mode)
    i._ZN3ZDT16position_controlEhththh       0x08005c74   Section        0  ZDT.o(i._ZN3ZDT16position_controlEhththh)
    i._ZN3ZDT4initEv                         0x08005d14   Section        0  ZDT.o(i._ZN3ZDT4initEv)
    i._ZN3ZDT9deal_dataEv                    0x08005d40   Section        0  ZDT.o(i._ZN3ZDT9deal_dataEv)
    i._ZN3ZDTC1EP20__UART_HandleTypeDefh     0x08005d68   Section        0  ZDT.o(i._ZN3ZDTC1EP20__UART_HandleTypeDefh)
    i._ZN4vofa13analysis_dataEv              0x08005d74   Section        0  vofa.o(i._ZN4vofa13analysis_dataEv)
    i._ZN4vofa4initEv                        0x08005e88   Section        0  vofa.o(i._ZN4vofa4initEv)
    i._ZN4vofa9read_dataEv                   0x08005ea8   Section        0  vofa.o(i._ZN4vofa9read_dataEv)
    i._ZN4vofaC1EP20__UART_HandleTypeDef     0x08005ee4   Section        0  vofa.o(i._ZN4vofaC1EP20__UART_HandleTypeDef)
    i._ZN5TruckC1Ev                          0x08005ee8   Section        0  truck.o(i._ZN5TruckC1Ev)
    i._ZN6TB661214encoder_updateEv           0x08005f0c   Section        0  motor.o(i._ZN6TB661214encoder_updateEv)
    i._ZN6TB66124initEv                      0x08006008   Section        0  motor.o(i._ZN6TB66124initEv)
    i._ZN6TB66128set_dutyEss                 0x08006040   Section        0  motor.o(i._ZN6TB66128set_dutyEss)
    i._ZN6TB66129set_duty1Es                 0x08006056   Section        0  motor.o(i._ZN6TB66129set_duty1Es)
    i._ZN6TB66129set_duty2Es                 0x080060ec   Section        0  motor.o(i._ZN6TB66129set_duty2Es)
    i._ZN6TB6612C1EP17TIM_HandleTypeDefjjP12GPIO_TypeDeftS3_tS3_tS3_tS1_S1_fffffbb 0x08006182   Section        0  motor.o(i._ZN6TB6612C1EP17TIM_HandleTypeDefjjP12GPIO_TypeDeftS3_tS3_tS3_tS1_S1_fffffbb)
    i._ZN6jy901s11sustain_yawEv              0x080061d4   Section        0  jy901s.o(i._ZN6jy901s11sustain_yawEv)
    i._ZN6jy901s4initEv                      0x08006230   Section        0  jy901s.o(i._ZN6jy901s4initEv)
    i._ZN6jy901s4zeroEv                      0x08006254   Section        0  jy901s.o(i._ZN6jy901s4zeroEv)
    i._ZN6jy901s6updateEv                    0x08006264   Section        0  jy901s.o(i._ZN6jy901s6updateEv)
    i._ZN6jy901sC1EP20__UART_HandleTypeDef   0x080062c8   Section        0  jy901s.o(i._ZN6jy901sC1EP20__UART_HandleTypeDef)
    i._ZN7maixcam4initEv                     0x0800630c   Section        0  maixcam.o(i._ZN7maixcam4initEv)
    i._ZN7maixcam6updateEv                   0x0800632c   Section        0  maixcam.o(i._ZN7maixcam6updateEv)
    i._ZN7maixcamC1EP20__UART_HandleTypeDef  0x08006374   Section        0  maixcam.o(i._ZN7maixcamC1EP20__UART_HandleTypeDef)
    i.__ARM_fpclassify                       0x08006378   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__NVIC_SetPriority                     0x080063a8   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x080063a9   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__cxa_guard_acquire                    0x080063c8   Section        0  cxa_guard_acquire.o(i.__cxa_guard_acquire)
    i.__sti___19_global_variable_cpp_f9d78f51 0x080063d8   Section        0  global_variable.o(i.__sti___19_global_variable_cpp_f9d78f51)
    __sti___19_global_variable_cpp_f9d78f51  0x080063d9   Thumb Code   520  global_variable.o(i.__sti___19_global_variable_cpp_f9d78f51)
    i._is_digit                              0x08006640   Section        0  __printf_wp.o(i._is_digit)
    i.draw_Task                              0x08006650   Section        0  my_task.o(i.draw_Task)
    i.main                                   0x08006674   Section        0  main.o(i.main)
    i.main_Task                              0x0800668e   Section        0  my_task.o(i.main_Task)
    i.osDelay                                0x08006690   Section        0  cmsis_os2.o(i.osDelay)
    i.osKernelInitialize                     0x080066ac   Section        0  cmsis_os2.o(i.osKernelInitialize)
    i.osKernelStart                          0x080066d0   Section        0  cmsis_os2.o(i.osKernelStart)
    i.osThreadNew                            0x08006708   Section        0  cmsis_os2.o(i.osThreadNew)
    i.prvAddCurrentTaskToDelayedList         0x08006794   Section        0  tasks.o(i.prvAddCurrentTaskToDelayedList)
    prvAddCurrentTaskToDelayedList           0x08006795   Thumb Code    84  tasks.o(i.prvAddCurrentTaskToDelayedList)
    i.prvAddNewTaskToReadyList               0x080067f0   Section        0  tasks.o(i.prvAddNewTaskToReadyList)
    prvAddNewTaskToReadyList                 0x080067f1   Thumb Code   190  tasks.o(i.prvAddNewTaskToReadyList)
    i.prvCheckForValidListAndQueue           0x080068c0   Section        0  timers.o(i.prvCheckForValidListAndQueue)
    prvCheckForValidListAndQueue             0x080068c1   Thumb Code    72  timers.o(i.prvCheckForValidListAndQueue)
    i.prvCopyDataFromQueue                   0x08006918   Section        0  queue.o(i.prvCopyDataFromQueue)
    prvCopyDataFromQueue                     0x08006919   Thumb Code    38  queue.o(i.prvCopyDataFromQueue)
    i.prvCopyDataToQueue                     0x0800693e   Section        0  queue.o(i.prvCopyDataToQueue)
    prvCopyDataToQueue                       0x0800693f   Thumb Code   108  queue.o(i.prvCopyDataToQueue)
    i.prvDeleteTCB                           0x080069aa   Section        0  tasks.o(i.prvDeleteTCB)
    prvDeleteTCB                             0x080069ab   Thumb Code    52  tasks.o(i.prvDeleteTCB)
    i.prvHeapInit                            0x080069e0   Section        0  heap_4.o(i.prvHeapInit)
    prvHeapInit                              0x080069e1   Thumb Code    66  heap_4.o(i.prvHeapInit)
    i.prvIdleTask                            0x08006a2c   Section        0  tasks.o(i.prvIdleTask)
    prvIdleTask                              0x08006a2d   Thumb Code    82  tasks.o(i.prvIdleTask)
    i.prvInitialiseNewQueue                  0x08006a8c   Section        0  queue.o(i.prvInitialiseNewQueue)
    prvInitialiseNewQueue                    0x08006a8d   Thumb Code    34  queue.o(i.prvInitialiseNewQueue)
    i.prvInitialiseNewTask                   0x08006ab0   Section        0  tasks.o(i.prvInitialiseNewTask)
    prvInitialiseNewTask                     0x08006ab1   Thumb Code   176  tasks.o(i.prvInitialiseNewTask)
    i.prvInsertBlockIntoFreeList             0x08006b60   Section        0  heap_4.o(i.prvInsertBlockIntoFreeList)
    prvInsertBlockIntoFreeList               0x08006b61   Thumb Code    72  heap_4.o(i.prvInsertBlockIntoFreeList)
    i.prvInsertTimerInActiveList             0x08006bac   Section        0  timers.o(i.prvInsertTimerInActiveList)
    prvInsertTimerInActiveList               0x08006bad   Thumb Code    52  timers.o(i.prvInsertTimerInActiveList)
    i.prvIsQueueEmpty                        0x08006be4   Section        0  queue.o(i.prvIsQueueEmpty)
    prvIsQueueEmpty                          0x08006be5   Thumb Code    28  queue.o(i.prvIsQueueEmpty)
    i.prvProcessReceivedCommands             0x08006c00   Section        0  timers.o(i.prvProcessReceivedCommands)
    prvProcessReceivedCommands               0x08006c01   Thumb Code   248  timers.o(i.prvProcessReceivedCommands)
    i.prvProcessTimerOrBlockTask             0x08006cfc   Section        0  timers.o(i.prvProcessTimerOrBlockTask)
    prvProcessTimerOrBlockTask               0x08006cfd   Thumb Code   182  timers.o(i.prvProcessTimerOrBlockTask)
    i.prvResetNextTaskUnblockTime            0x08006dbc   Section        0  tasks.o(i.prvResetNextTaskUnblockTime)
    prvResetNextTaskUnblockTime              0x08006dbd   Thumb Code    26  tasks.o(i.prvResetNextTaskUnblockTime)
    i.prvSampleTimeNow                       0x08006ddc   Section        0  timers.o(i.prvSampleTimeNow)
    prvSampleTimeNow                         0x08006ddd   Thumb Code    36  timers.o(i.prvSampleTimeNow)
    i.prvSwitchTimerLists                    0x08006e04   Section        0  timers.o(i.prvSwitchTimerLists)
    prvSwitchTimerLists                      0x08006e05   Thumb Code   104  timers.o(i.prvSwitchTimerLists)
    i.prvTaskExitError                       0x08006e70   Section        0  port.o(i.prvTaskExitError)
    prvTaskExitError                         0x08006e71   Thumb Code    36  port.o(i.prvTaskExitError)
    i.prvTimerTask                           0x08006e98   Section        0  timers.o(i.prvTimerTask)
    prvTimerTask                             0x08006e99   Thumb Code    32  timers.o(i.prvTimerTask)
    i.prvUnlockQueue                         0x08006ebc   Section        0  queue.o(i.prvUnlockQueue)
    prvUnlockQueue                           0x08006ebd   Thumb Code   106  queue.o(i.prvUnlockQueue)
    i.pvPortMalloc                           0x08006f28   Section        0  heap_4.o(i.pvPortMalloc)
    i.pxPortInitialiseStack                  0x08007004   Section        0  port.o(i.pxPortInitialiseStack)
    i.uxListRemove                           0x08007030   Section        0  list.o(i.uxListRemove)
    i.vApplicationGetIdleTaskMemory          0x08007058   Section        0  cmsis_os2.o(i.vApplicationGetIdleTaskMemory)
    i.vApplicationGetTimerTaskMemory         0x0800706c   Section        0  cmsis_os2.o(i.vApplicationGetTimerTaskMemory)
    i.vListInitialise                        0x08007084   Section        0  list.o(i.vListInitialise)
    i.vListInitialiseItem                    0x0800709a   Section        0  list.o(i.vListInitialiseItem)
    i.vListInsert                            0x080070a0   Section        0  list.o(i.vListInsert)
    i.vListInsertEnd                         0x080070d0   Section        0  list.o(i.vListInsertEnd)
    i.vPortEnterCritical                     0x080070e8   Section        0  port.o(i.vPortEnterCritical)
    i.vPortExitCritical                      0x08007128   Section        0  port.o(i.vPortExitCritical)
    i.vPortFree                              0x08007150   Section        0  heap_4.o(i.vPortFree)
    i.vPortSetupTimerInterrupt               0x080071b4   Section        0  port.o(i.vPortSetupTimerInterrupt)
    i.vPortValidateInterruptPriority         0x080071d8   Section        0  port.o(i.vPortValidateInterruptPriority)
    i.vQueueAddToRegistry                    0x0800722c   Section        0  queue.o(i.vQueueAddToRegistry)
    i.vQueueWaitForMessageRestricted         0x08007254   Section        0  queue.o(i.vQueueWaitForMessageRestricted)
    i.vTaskDelay                             0x08007298   Section        0  tasks.o(i.vTaskDelay)
    i.vTaskInternalSetTimeOutState           0x080072e4   Section        0  tasks.o(i.vTaskInternalSetTimeOutState)
    i.vTaskMissedYield                       0x080072f4   Section        0  tasks.o(i.vTaskMissedYield)
    i.vTaskPlaceOnEventList                  0x08007300   Section        0  tasks.o(i.vTaskPlaceOnEventList)
    i.vTaskPlaceOnEventListRestricted        0x08007330   Section        0  tasks.o(i.vTaskPlaceOnEventListRestricted)
    i.vTaskStartScheduler                    0x08007368   Section        0  tasks.o(i.vTaskStartScheduler)
    i.vTaskSuspendAll                        0x080073f0   Section        0  tasks.o(i.vTaskSuspendAll)
    i.vTaskSwitchContext                     0x08007400   Section        0  tasks.o(i.vTaskSwitchContext)
    i.vofa_Task                              0x08007464   Section        0  my_task.o(i.vofa_Task)
    i.xPortStartScheduler                    0x080074a8   Section        0  port.o(i.xPortStartScheduler)
    i.xPortSysTickHandler                    0x0800759c   Section        0  port.o(i.xPortSysTickHandler)
    i.xQueueGenericCreateStatic              0x080075c8   Section        0  queue.o(i.xQueueGenericCreateStatic)
    i.xQueueGenericReset                     0x08007630   Section        0  queue.o(i.xQueueGenericReset)
    i.xQueueGenericSend                      0x080076b8   Section        0  queue.o(i.xQueueGenericSend)
    i.xQueueGenericSendFromISR               0x08007818   Section        0  queue.o(i.xQueueGenericSendFromISR)
    i.xQueueReceive                          0x080078d8   Section        0  queue.o(i.xQueueReceive)
    i.xTaskCheckForTimeOut                   0x08007a10   Section        0  tasks.o(i.xTaskCheckForTimeOut)
    i.xTaskCreate                            0x08007a84   Section        0  tasks.o(i.xTaskCreate)
    i.xTaskCreateStatic                      0x08007ade   Section        0  tasks.o(i.xTaskCreateStatic)
    i.xTaskGetSchedulerState                 0x08007b34   Section        0  tasks.o(i.xTaskGetSchedulerState)
    i.xTaskGetTickCount                      0x08007b50   Section        0  tasks.o(i.xTaskGetTickCount)
    i.xTaskIncrementTick                     0x08007b5c   Section        0  tasks.o(i.xTaskIncrementTick)
    i.xTaskPriorityDisinherit                0x08007c24   Section        0  tasks.o(i.xTaskPriorityDisinherit)
    i.xTaskRemoveFromEventList               0x08007ca4   Section        0  tasks.o(i.xTaskRemoveFromEventList)
    i.xTaskResumeAll                         0x08007d14   Section        0  tasks.o(i.xTaskResumeAll)
    i.xTimerCreateTimerTask                  0x08007dd8   Section        0  timers.o(i.xTimerCreateTimerTask)
    i.xTimerGenericCommand                   0x08007e38   Section        0  timers.o(i.xTimerGenericCommand)
    locale$$code                             0x08007ea0   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$dretinf                            0x08007ecc   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x08007ecc   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$f2d                                0x08007ed8   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x08007ed8   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x08007f2e   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x08007f2e   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x08007fba   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08007fba   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$printf1                            0x08007fc4   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x08007fc4   Number         0  printf1.o(x$fpl$printf1)
    .constdata                               0x08007fc8   Section     1520  font.o(.constdata)
    x$fpl$usenofp                            0x08007fc8   Section        0  usenofp.o(x$fpl$usenofp)
    ascii_16x8                               0x08007fc8   Data        1520  font.o(.constdata)
    .constdata                               0x080085b8   Section        8  font.o(.constdata)
    .constdata                               0x080085c0   Section      144  font.o(.constdata)
    zh16x16                                  0x080085c0   Data         144  font.o(.constdata)
    .constdata                               0x08008650   Section       16  font.o(.constdata)
    .constdata                               0x08008660   Section      252  freertos.o(.constdata)
    .constdata                               0x0800875c   Section       16  system_stm32f4xx.o(.constdata)
    .constdata                               0x0800876c   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x08008774   Section        8  stm32f4xx_hal_dma.o(.constdata)
    flagBitshiftOffset                       0x08008774   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x0800877c   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x0800877c   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x080087b8   Data          64  bigflt0.o(.constdata)
    .conststring                             0x08008810   Section       53  freertos.o(.conststring)
    locale$$data                             0x08008868   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x0800886c   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08008874   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08008880   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x08008882   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x08008883   Data           0  lc_numeric_c.o(locale$$data)
    .init_array                              0x08008884   Section        4  global_variable.o(.init_array)
    __lcnum_c_end                            0x08008884   Data           0  lc_numeric_c.o(locale$$data)
    .init_array                              0x08008888   Section        0  init_aeabi.o(.init_array)
    .data                                    0x20000000   Section       20  ZDT.o(.data)
    _ZGVpulse_count                          0x20000000   Data           4  ZDT.o(.data)
    pulse_count                              0x20000004   Data           4  ZDT.o(.data)
    the_same_order                           0x20000008   Data           4  ZDT.o(.data)
    speed_control_order                      0x2000000c   Data           8  ZDT.o(.data)
    .data                                    0x20000014   Section       28  freertos.o(.data)
    .data                                    0x20000030   Section       28  global_variable.o(.data)
    .data                                    0x2000004c   Section        4  global_variable.o(.data)
    .data                                    0x20000050   Section        4  global_variable.o(.data)
    .data                                    0x20000054   Section        8  global_variable.o(.data)
    .data                                    0x2000005c   Section        8  motor.o(.data)
    last_rpm_L                               0x2000005c   Data           4  motor.o(.data)
    last_rpm_R                               0x20000060   Data           4  motor.o(.data)
    .data                                    0x20000064   Section        4  oled.o(.data)
    temp                                     0x20000064   Data           1  oled.o(.data)
    temp                                     0x20000065   Data           1  oled.o(.data)
    sendBuffer                               0x20000066   Data           2  oled.o(.data)
    .data                                    0x20000068   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x2000006c   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x20000078   Section        4  cmsis_os2.o(.data)
    KernelState                              0x20000078   Data           4  cmsis_os2.o(.data)
    .data                                    0x2000007c   Section       32  heap_4.o(.data)
    pxEnd                                    0x2000007c   Data           4  heap_4.o(.data)
    xFreeBytesRemaining                      0x20000080   Data           4  heap_4.o(.data)
    xMinimumEverFreeBytesRemaining           0x20000084   Data           4  heap_4.o(.data)
    xNumberOfSuccessfulAllocations           0x20000088   Data           4  heap_4.o(.data)
    xNumberOfSuccessfulFrees                 0x2000008c   Data           4  heap_4.o(.data)
    xBlockAllocatedBit                       0x20000090   Data           4  heap_4.o(.data)
    xStart                                   0x20000094   Data           8  heap_4.o(.data)
    .data                                    0x2000009c   Section       12  port.o(.data)
    ucMaxSysCallPriority                     0x2000009c   Data           1  port.o(.data)
    uxCriticalNesting                        0x200000a0   Data           4  port.o(.data)
    ulMaxPRIGROUPValue                       0x200000a4   Data           4  port.o(.data)
    .data                                    0x200000a8   Section       60  tasks.o(.data)
    uxDeletedTasksWaitingCleanUp             0x200000ac   Data           4  tasks.o(.data)
    uxCurrentNumberOfTasks                   0x200000b0   Data           4  tasks.o(.data)
    xTickCount                               0x200000b4   Data           4  tasks.o(.data)
    uxTopReadyPriority                       0x200000b8   Data           4  tasks.o(.data)
    xSchedulerRunning                        0x200000bc   Data           4  tasks.o(.data)
    xPendedTicks                             0x200000c0   Data           4  tasks.o(.data)
    xYieldPending                            0x200000c4   Data           4  tasks.o(.data)
    xNumOfOverflows                          0x200000c8   Data           4  tasks.o(.data)
    uxTaskNumber                             0x200000cc   Data           4  tasks.o(.data)
    xNextTaskUnblockTime                     0x200000d0   Data           4  tasks.o(.data)
    xIdleTaskHandle                          0x200000d4   Data           4  tasks.o(.data)
    uxSchedulerSuspended                     0x200000d8   Data           4  tasks.o(.data)
    pxDelayedTaskList                        0x200000dc   Data           4  tasks.o(.data)
    pxOverflowDelayedTaskList                0x200000e0   Data           4  tasks.o(.data)
    .data                                    0x200000e4   Section       20  timers.o(.data)
    xTimerQueue                              0x200000e4   Data           4  timers.o(.data)
    xTimerTaskHandle                         0x200000e8   Data           4  timers.o(.data)
    xLastTime                                0x200000ec   Data           4  timers.o(.data)
    pxCurrentTimerList                       0x200000f0   Data           4  timers.o(.data)
    pxOverflowTimerList                      0x200000f4   Data           4  timers.o(.data)
    .bss                                     0x200000f8   Section       13  ZDT.o(.bss)
    position_control_order                   0x200000f8   Data          13  ZDT.o(.bss)
    .bss                                     0x20000108   Section      512  freertos.o(.bss)
    .bss                                     0x20000308   Section       92  freertos.o(.bss)
    .bss                                     0x20000364   Section     1024  freertos.o(.bss)
    .bss                                     0x20000764   Section       92  freertos.o(.bss)
    .bss                                     0x200007c0   Section     2048  freertos.o(.bss)
    .bss                                     0x20000fc0   Section       92  freertos.o(.bss)
    .bss                                     0x2000101c   Section     1024  freertos.o(.bss)
    .bss                                     0x2000141c   Section       92  freertos.o(.bss)
    .bss                                     0x20001478   Section     2048  freertos.o(.bss)
    .bss                                     0x20001c78   Section       92  freertos.o(.bss)
    .bss                                     0x20001cd4   Section     2048  freertos.o(.bss)
    .bss                                     0x200024d4   Section       92  freertos.o(.bss)
    .bss                                     0x20002530   Section      768  global_variable.o(.bss)
    .bss                                     0x20002830   Section       84  i2c.o(.bss)
    .bss                                     0x20002884   Section      160  my_task.o(.bss)
    data_string                              0x20002884   Data          80  my_task.o(.bss)
    data_string                              0x200028d4   Data          80  my_task.o(.bss)
    .bss                                     0x20002924   Section     1153  oled.o(.bss)
    sendBuffer                               0x20002924   Data         129  oled.o(.bss)
    .bss                                     0x20002da8   Section       72  stm32f4xx_hal_timebase_tim.o(.bss)
    .bss                                     0x20002df0   Section      288  tim.o(.bss)
    .bss                                     0x20002f10   Section     1056  usart.o(.bss)
    .bss                                     0x20003330   Section     1720  cmsis_os2.o(.bss)
    Idle_TCB                                 0x20003330   Data          92  cmsis_os2.o(.bss)
    Idle_Stack                               0x2000338c   Data         512  cmsis_os2.o(.bss)
    Timer_TCB                                0x2000358c   Data          92  cmsis_os2.o(.bss)
    Timer_Stack                              0x200035e8   Data        1024  cmsis_os2.o(.bss)
    .bss                                     0x200039e8   Section    15360  heap_4.o(.bss)
    ucHeap                                   0x200039e8   Data       15360  heap_4.o(.bss)
    .bss                                     0x200075e8   Section       64  queue.o(.bss)
    .bss                                     0x20007628   Section     1220  tasks.o(.bss)
    pxReadyTasksLists                        0x20007628   Data        1120  tasks.o(.bss)
    xDelayedTaskList1                        0x20007a88   Data          20  tasks.o(.bss)
    xDelayedTaskList2                        0x20007a9c   Data          20  tasks.o(.bss)
    xPendingReadyList                        0x20007ab0   Data          20  tasks.o(.bss)
    xTasksWaitingTermination                 0x20007ac4   Data          20  tasks.o(.bss)
    xSuspendedTaskList                       0x20007ad8   Data          20  tasks.o(.bss)
    .bss                                     0x20007aec   Section      280  timers.o(.bss)
    xStaticTimerQueue                        0x20007aec   Data          80  timers.o(.bss)
    ucStaticTimerQueueStorage                0x20007b3c   Data         160  timers.o(.bss)
    xActiveTimerList1                        0x20007bdc   Data          20  timers.o(.bss)
    xActiveTimerList2                        0x20007bf0   Data          20  timers.o(.bss)
    .bss                                     0x20007c04   Section       96  libspace.o(.bss)
    HEAP                                     0x20007c68   Section      512  startup_stm32f407xx.o(HEAP)
    Heap_Mem                                 0x20007c68   Data         512  startup_stm32f407xx.o(HEAP)
    STACK                                    0x20007e68   Section     1024  startup_stm32f407xx.o(STACK)
    Stack_Mem                                0x20007e68   Data        1024  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x20008268   Data           0  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x080001fd   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x080001fd   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x08000203   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_percent_end                      0x08000209   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x0800020d   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000213   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000219   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x08000223   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000223   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000223   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000223   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_2                      0x08000223   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000031)
    __rt_lib_init_exceptions_1               0x08000223   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000223   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000223   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x08000223   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000223   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_signal_1                   0x08000223   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000223   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_cpp_1                      0x08000227   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_return                     0x08000227   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_shutdown                        0x08000229   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0800022b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0800022b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x0800022b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x0800022b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x0800022b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x0800022b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x0800022b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x0800022d   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0800022d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0800022d   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000233   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000233   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000237   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000237   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800023f   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000241   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000241   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000245   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    SVC_Handler                              0x0800024d   Thumb Code    28  port.o(.emb_text)
    __asm___6_port_c_39a90d8d__prvStartFirstTask 0x0800026d   Thumb Code    36  port.o(.emb_text)
    __asm___6_port_c_39a90d8d__prvEnableVFP  0x08000295   Thumb Code    16  port.o(.emb_text)
    PendSV_Handler                           0x080002a9   Thumb Code    88  port.o(.emb_text)
    vPortGetIPSR                             0x08000305   Thumb Code     6  port.o(.emb_text)
    Reset_Handler                            0x0800030d   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x08000327   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x08000329   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x0800034d   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x0800034d   Thumb Code   238  lludivv7m.o(.text)
    __2sprintf                               0x0800043d   Thumb Code    34  noretval__2sprintf.o(.text)
    _printf_int_dec                          0x08000465   Thumb Code   104  _printf_dec.o(.text)
    __printf                                 0x080004dd   Thumb Code   270  __printf_wp.o(.text)
    memcmp                                   0x080005eb   Thumb Code    88  memcmp.o(.text)
    strlen                                   0x08000643   Thumb Code    62  strlen.o(.text)
    __aeabi_memcpy                           0x08000681   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x08000681   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x080006e7   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memset                           0x0800070b   Thumb Code    16  aeabi_memset.o(.text)
    __aeabi_memclr                           0x0800071b   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x0800071b   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x0800071f   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x0800075f   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x0800075f   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x0800075f   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x08000763   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x080007ad   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080007af   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080007b1   Thumb Code     2  heapauxi.o(.text)
    __cpp_initialize__aeabi_                 0x080007b5   Thumb Code    26  init_aeabi.o(.text)
    _printf_int_common                       0x080007d9   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x0800088b   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000a3d   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08000cb3   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000cd9   Thumb Code    10  _sputc.o(.text)
    __aeabi_memcpy4                          0x08000ce3   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08000ce3   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08000ce3   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08000d2b   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_locale                              0x08000d49   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _ll_udiv10                               0x08000d51   Thumb Code   138  lludiv10.o(.text)
    _printf_fp_infnan                        0x08000ddd   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08000e5d   Thumb Code   224  bigflt0.o(.text)
    __user_libspace                          0x08000f41   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000f41   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000f41   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08000f49   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000f93   Thumb Code    18  exit.o(.text)
    strcmp                                   0x08000fa5   Thumb Code   128  strcmpv7m.o(.text)
    _sys_exit                                0x08001025   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08001031   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08001031   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08001033   Thumb Code     0  indicate_semi.o(.text)
    _btod_d2e                                0x08001033   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08001071   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x080010b7   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08001117   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x0800144f   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x0800152b   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08001555   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x0800157f   Thumb Code   580  btod.o(CL$$btod_mult_common)
    BusFault_Handler                         0x080017c3   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    DMA1_Stream1_IRQHandler                  0x080017c5   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream1_IRQHandler)
    DMA1_Stream3_IRQHandler                  0x080017d1   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream3_IRQHandler)
    DMA1_Stream5_IRQHandler                  0x080017dd   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler)
    DMA1_Stream6_IRQHandler                  0x080017e9   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream6_IRQHandler)
    DMA2_Stream1_IRQHandler                  0x080017f5   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler)
    DMA2_Stream2_IRQHandler                  0x08001801   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    DMA2_Stream6_IRQHandler                  0x0800180d   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream6_IRQHandler)
    DMA2_Stream7_IRQHandler                  0x08001819   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream7_IRQHandler)
    DebugMon_Handler                         0x080018c9   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Encoder_Task                             0x080018cd   Thumb Code    60  my_task.o(i.Encoder_Task)
    Error_Handler                            0x08001919   Thumb Code     4  main.o(i.Error_Handler)
    HAL_DMA_Abort                            0x0800191d   Thumb Code   146  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x080019af   Thumb Code    36  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_GetState                         0x080019d3   Thumb Code     6  stm32f4xx_hal_dma.o(i.HAL_DMA_GetState)
    HAL_DMA_IRQHandler                       0x080019d9   Thumb Code   412  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08001b79   Thumb Code   206  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08001c4d   Thumb Code   110  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x08001cbd   Thumb Code    32  stm32f4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x08001ce1   Thumb Code   450  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_TogglePin                       0x08001ed1   Thumb Code    16  stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    HAL_GPIO_WritePin                        0x08001ee1   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08001eed   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_I2C_AbortCpltCallback                0x08001ef9   Thumb Code     2  stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback)
    HAL_I2C_AddrCallback                     0x08001efb   Thumb Code     2  stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback)
    HAL_I2C_ER_IRQHandler                    0x08001efd   Thumb Code   186  stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler)
    HAL_I2C_EV_IRQHandler                    0x08001fb7   Thumb Code   560  stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler)
    HAL_I2C_ErrorCallback                    0x080021e7   Thumb Code     2  stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback)
    HAL_I2C_Init                             0x080021e9   Thumb Code   376  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_ListenCpltCallback               0x08002371   Thumb Code     2  stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback)
    HAL_I2C_MasterRxCpltCallback             0x08002373   Thumb Code     2  stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback)
    HAL_I2C_MasterTxCpltCallback             0x08002375   Thumb Code     2  stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback)
    HAL_I2C_Master_Transmit                  0x08002379   Thumb Code   290  stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    HAL_I2C_MemRxCpltCallback                0x080024a5   Thumb Code     2  stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback)
    HAL_I2C_MemTxCpltCallback                0x080024a7   Thumb Code     2  stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback)
    HAL_I2C_MspInit                          0x080024a9   Thumb Code   124  i2c.o(i.HAL_I2C_MspInit)
    HAL_I2C_SlaveRxCpltCallback              0x08002531   Thumb Code     2  stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback)
    HAL_I2C_SlaveTxCpltCallback              0x08002533   Thumb Code     2  stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback)
    HAL_IncTick                              0x08002535   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08002545   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08002579   Thumb Code   132  stm32f4xx_hal_timebase_tim.o(i.HAL_InitTick)
    HAL_MspInit                              0x08002611   Thumb Code    52  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08002649   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08002665   Thumb Code    60  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x080026a5   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x080026c9   Thumb Code   288  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetClockConfig                   0x080027fd   Thumb Code    54  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x0800283d   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x0800285d   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x0800287d   Thumb Code    88  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x080028dd   Thumb Code   856  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_TIMEx_BreakCallback                  0x08002c49   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x08002c4b   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_ConfigBreakDeadTime            0x08002c4d   Thumb Code    84  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_MasterConfigSynchronization    0x08002ca1   Thumb Code   116  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08002d31   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08002d8d   Thumb Code    50  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x08002dc9   Thumb Code   100  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_Encoder_Init                     0x08002e49   Thumb Code   164  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x08002eed   Thumb Code   184  tim.o(i.HAL_TIM_Encoder_MspInit)
    HAL_TIM_Encoder_Start                    0x08002fb5   Thumb Code   142  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    HAL_TIM_IC_CaptureCallback               0x08003043   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x08003045   Thumb Code   304  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_MspPostInit                      0x08003175   Thumb Code   108  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_OC_DelayElapsedCallback          0x080031f1   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_ConfigChannel                0x080031f3   Thumb Code   204  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x080032bf   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08003319   Thumb Code    30  tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_PulseFinishedCallback        0x08003341   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PWM_Start                        0x08003345   Thumb Code   172  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_TIM_PeriodElapsedCallback            0x0800340d   Thumb Code    28  main.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08003435   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08003437   Thumb Code    78  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x08003485   Thumb Code   298  callback.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x080035d5   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x080035d9   Thumb Code   618  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08003849   Thumb Code   100  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x080038ad   Thumb Code   830  usart.o(i.HAL_UART_MspInit)
    HAL_UART_RxCpltCallback                  0x08003c21   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x08003c23   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit_DMA                    0x08003c25   Thumb Code   106  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA)
    HAL_UART_TxCpltCallback                  0x08003c9d   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HAL_UART_TxHalfCpltCallback              0x08003c9f   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback)
    HardFault_Handler                        0x08003ca1   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    I2C1_ER_IRQHandler                       0x08003ca5   Thumb Code     6  stm32f4xx_it.o(i.I2C1_ER_IRQHandler)
    I2C1_EV_IRQHandler                       0x08003cb1   Thumb Code     6  stm32f4xx_it.o(i.I2C1_EV_IRQHandler)
    LED_Task                                 0x08004931   Thumb Code    26  my_task.o(i.LED_Task)
    MX_DMA_Init                              0x08004951   Thumb Code   168  dma.o(i.MX_DMA_Init)
    MX_FREERTOS_Init                         0x080049fd   Thumb Code   102  freertos.o(i.MX_FREERTOS_Init)
    MX_GPIO_Init                             0x08004a89   Thumb Code   298  gpio.o(i.MX_GPIO_Init)
    MX_I2C1_Init                             0x08004bcd   Thumb Code    50  i2c.o(i.MX_I2C1_Init)
    MX_TIM1_Init                             0x08004c0d   Thumb Code   168  tim.o(i.MX_TIM1_Init)
    MX_TIM2_Init                             0x08004cbd   Thumb Code    98  tim.o(i.MX_TIM2_Init)
    MX_TIM3_Init                             0x08004d25   Thumb Code    98  tim.o(i.MX_TIM3_Init)
    MX_TIM6_Init                             0x08004d91   Thumb Code    60  tim.o(i.MX_TIM6_Init)
    MX_USART1_UART_Init                      0x08004dd5   Thumb Code    48  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08004e0d   Thumb Code    48  usart.o(i.MX_USART2_UART_Init)
    MX_USART3_UART_Init                      0x08004e45   Thumb Code    48  usart.o(i.MX_USART3_UART_Init)
    MX_USART6_UART_Init                      0x08004e7d   Thumb Code    48  usart.o(i.MX_USART6_UART_Init)
    MemManage_Handler                        0x08004eb5   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08004eb7   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    OLED_Task                                0x08004eb9   Thumb Code    60  my_task.o(i.OLED_Task)
    StartDefaultTask                         0x08004f09   Thumb Code     8  freertos.o(i.StartDefaultTask)
    SysTick_Handler                          0x08004f11   Thumb Code    26  cmsis_os2.o(i.SysTick_Handler)
    SystemInit                               0x08004f2d   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    TIM6_DAC_IRQHandler                      0x08004f3d   Thumb Code     6  stm32f4xx_it.o(i.TIM6_DAC_IRQHandler)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08004f49   Thumb Code     6  stm32f4xx_it.o(i.TIM8_TRG_COM_TIM14_IRQHandler)
    TIM_Base_SetConfig                       0x08004f55   Thumb Code   170  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x0800502d   Thumb Code    26  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_OC2_SetConfig                        0x080050a9   Thumb Code    98  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    UART_Start_Receive_DMA                   0x08005551   Thumb Code   130  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    USART1_IRQHandler                        0x080055e1   Thumb Code     6  stm32f4xx_it.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x080055ed   Thumb Code     6  stm32f4xx_it.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x080055f9   Thumb Code     6  stm32f4xx_it.o(i.USART3_IRQHandler)
    USART6_IRQHandler                        0x08005605   Thumb Code     6  stm32f4xx_it.o(i.USART6_IRQHandler)
    UsageFault_Handler                       0x08005611   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    system_init()                            0x08005615   Thumb Code   128  global_variable.o(i._Z11system_initv)
    OLED_SendCmd(unsigned char)              0x080056a5   Thumb Code    12  oled.o(i._Z12OLED_SendCmdh)
    OLED_SetBits(unsigned char, unsigned char, unsigned char, OLED_ColorMode) 0x080056b5   Thumb Code    72  oled.o(i._Z12OLED_SetBitshhh14OLED_ColorMode)
    OLED_NewFrame()                          0x080056fd   Thumb Code    10  oled.o(i._Z13OLED_NewFramev)
    OLED_SetBlock(unsigned char, unsigned char, const unsigned char*, unsigned char, unsigned char, OLED_ColorMode) 0x0800570d   Thumb Code   136  oled.o(i._Z13OLED_SetBlockhhPKhhh14OLED_ColorMode)
    OLED_ShowFrame()                         0x08005795   Thumb Code    68  oled.o(i._Z14OLED_ShowFramev)
    OLED_PrintString(unsigned char, unsigned char, char*, const Font*, OLED_ColorMode) 0x080057dd   Thumb Code   184  oled.o(i._Z16OLED_PrintStringhhPcPK4Font14OLED_ColorMode)
    _OLED_GetUTF8Len(char*)                  0x08005895   Thumb Code    50  oled.o(i._Z16_OLED_GetUTF8LenPc)
    OLED_SetBits_Fine(unsigned char, unsigned char, unsigned char, unsigned char, OLED_ColorMode) 0x080058c7   Thumb Code   104  oled.o(i._Z17OLED_SetBits_Finehhhh14OLED_ColorMode)
    OLED_SetByte_Fine(unsigned char, unsigned char, unsigned char, unsigned char, unsigned char, OLED_ColorMode) 0x08005931   Thumb Code    64  oled.o(i._Z17OLED_SetByte_Finehhhhh14OLED_ColorMode)
    SystemClock_Config()                     0x08005979   Thumb Code   140  main.o(i._Z18SystemClock_Configv)
    OLED_PrintASCIIChar(unsigned char, unsigned char, char, const ASCIIFont*, OLED_ColorMode) 0x08005a0d   Thumb Code    34  oled.o(i._Z19OLED_PrintASCIICharhhcPK9ASCIIFont14OLED_ColorMode)
    OLED_Init()                              0x08005a2f   Thumb Code   182  oled.o(i._Z9OLED_Initv)
    OLED_Send(unsigned char*, unsigned char) 0x08005ae5   Thumb Code    22  oled.o(i._Z9OLED_SendPhh)
    PID_Controller::pid_figure()             0x08005b01   Thumb Code   234  pid.o(i._ZN14PID_Controller10pid_figureEv)
    PID_Controller::dead_zone()              0x08005bf1   Thumb Code    54  pid.o(i._ZN14PID_Controller9dead_zoneEv)
    PID_Controller::PID_Controller(float, float, float, pid_mode, float*, float*, float*, float, bool, float) 0x08005c2d   Thumb Code    60  pid.o(i._ZN14PID_ControllerC1Efff8pid_modePfS1_S1_fbf)
    PID_Controller::PID_Controller__sub_object(float, float, float, pid_mode, float*, float*, float*, float, bool, float) 0x08005c2d   Thumb Code     0  pid.o(i._ZN14PID_ControllerC1Efff8pid_modePfS1_S1_fbf)
    KEY::KEY(GPIO_TypeDef*, unsigned short, KEY_Mode) 0x08005c6d   Thumb Code     8  key.o(i._ZN3KEYC1EP12GPIO_TypeDeft8KEY_Mode)
    KEY::KEY__sub_object(GPIO_TypeDef*, unsigned short, KEY_Mode) 0x08005c6d   Thumb Code     0  key.o(i._ZN3KEYC1EP12GPIO_TypeDeft8KEY_Mode)
    ZDT::position_control(unsigned char, unsigned short, unsigned char, unsigned short, unsigned char, unsigned char) 0x08005c75   Thumb Code   148  ZDT.o(i._ZN3ZDT16position_controlEhththh)
    ZDT::init()                              0x08005d15   Thumb Code    44  ZDT.o(i._ZN3ZDT4initEv)
    ZDT::deal_data()                         0x08005d41   Thumb Code    34  ZDT.o(i._ZN3ZDT9deal_dataEv)
    ZDT::ZDT(__UART_HandleTypeDef*, unsigned char) 0x08005d69   Thumb Code    10  ZDT.o(i._ZN3ZDTC1EP20__UART_HandleTypeDefh)
    ZDT::ZDT__sub_object(__UART_HandleTypeDef*, unsigned char) 0x08005d69   Thumb Code     0  ZDT.o(i._ZN3ZDTC1EP20__UART_HandleTypeDefh)
    vofa::analysis_data()                    0x08005d75   Thumb Code   258  vofa.o(i._ZN4vofa13analysis_dataEv)
    vofa::init()                             0x08005e89   Thumb Code    30  vofa.o(i._ZN4vofa4initEv)
    vofa::read_data()                        0x08005ea9   Thumb Code    56  vofa.o(i._ZN4vofa9read_dataEv)
    vofa::vofa(__UART_HandleTypeDef*)        0x08005ee5   Thumb Code     4  vofa.o(i._ZN4vofaC1EP20__UART_HandleTypeDef)
    vofa::vofa__sub_object(__UART_HandleTypeDef*) 0x08005ee5   Thumb Code     0  vofa.o(i._ZN4vofaC1EP20__UART_HandleTypeDef)
    Truck::Truck()                           0x08005ee9   Thumb Code    34  truck.o(i._ZN5TruckC1Ev)
    Truck::Truck__sub_object()               0x08005ee9   Thumb Code     0  truck.o(i._ZN5TruckC1Ev)
    TB6612::encoder_update()                 0x08005f0d   Thumb Code   244  motor.o(i._ZN6TB661214encoder_updateEv)
    TB6612::init()                           0x08006009   Thumb Code    56  motor.o(i._ZN6TB66124initEv)
    TB6612::set_duty(short, short)           0x08006041   Thumb Code    22  motor.o(i._ZN6TB66128set_dutyEss)
    TB6612::set_duty1(short)                 0x08006057   Thumb Code   150  motor.o(i._ZN6TB66129set_duty1Es)
    TB6612::set_duty2(short)                 0x080060ed   Thumb Code   150  motor.o(i._ZN6TB66129set_duty2Es)
    TB6612::TB6612(TIM_HandleTypeDef*, unsigned, unsigned, GPIO_TypeDef*, unsigned short, GPIO_TypeDef*, unsigned short, GPIO_TypeDef*, unsigned short, GPIO_TypeDef*, unsigned short, TIM_HandleTypeDef*, TIM_HandleTypeDef*, float, float, float, float, float, bool, bool) 0x08006183   Thumb Code    82  motor.o(i._ZN6TB6612C1EP17TIM_HandleTypeDefjjP12GPIO_TypeDeftS3_tS3_tS3_tS1_S1_fffffbb)
    TB6612::TB6612__sub_object(TIM_HandleTypeDef*, unsigned, unsigned, GPIO_TypeDef*, unsigned short, GPIO_TypeDef*, unsigned short, GPIO_TypeDef*, unsigned short, GPIO_TypeDef*, unsigned short, TIM_HandleTypeDef*, TIM_HandleTypeDef*, float, float, float, float, float, bool, bool) 0x08006183   Thumb Code     0  motor.o(i._ZN6TB6612C1EP17TIM_HandleTypeDefjjP12GPIO_TypeDeftS3_tS3_tS3_tS1_S1_fffffbb)
    jy901s::sustain_yaw()                    0x080061d5   Thumb Code    78  jy901s.o(i._ZN6jy901s11sustain_yawEv)
    jy901s::init()                           0x08006231   Thumb Code    36  jy901s.o(i._ZN6jy901s4initEv)
    jy901s::zero()                           0x08006255   Thumb Code    14  jy901s.o(i._ZN6jy901s4zeroEv)
    jy901s::update()                         0x08006265   Thumb Code    90  jy901s.o(i._ZN6jy901s6updateEv)
    jy901s::jy901s(__UART_HandleTypeDef*)    0x080062c9   Thumb Code    62  jy901s.o(i._ZN6jy901sC1EP20__UART_HandleTypeDef)
    jy901s::jy901s__sub_object(__UART_HandleTypeDef*) 0x080062c9   Thumb Code     0  jy901s.o(i._ZN6jy901sC1EP20__UART_HandleTypeDef)
    maixcam::init()                          0x0800630d   Thumb Code    30  maixcam.o(i._ZN7maixcam4initEv)
    maixcam::update()                        0x0800632d   Thumb Code    62  maixcam.o(i._ZN7maixcam6updateEv)
    maixcam::maixcam(__UART_HandleTypeDef*)  0x08006375   Thumb Code     4  maixcam.o(i._ZN7maixcamC1EP20__UART_HandleTypeDef)
    maixcam::maixcam__sub_object(__UART_HandleTypeDef*) 0x08006375   Thumb Code     0  maixcam.o(i._ZN7maixcamC1EP20__UART_HandleTypeDef)
    __ARM_fpclassify                         0x08006379   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __cxa_guard_acquire                      0x080063c9   Thumb Code    16  cxa_guard_acquire.o(i.__cxa_guard_acquire)
    _is_digit                                0x08006641   Thumb Code    14  __printf_wp.o(i._is_digit)
    draw_Task                                0x08006651   Thumb Code    30  my_task.o(i.draw_Task)
    main                                     0x08006675   Thumb Code    26  main.o(i.main)
    main_Task                                0x0800668f   Thumb Code     2  my_task.o(i.main_Task)
    osDelay                                  0x08006691   Thumb Code    26  cmsis_os2.o(i.osDelay)
    osKernelInitialize                       0x080066ad   Thumb Code    32  cmsis_os2.o(i.osKernelInitialize)
    osKernelStart                            0x080066d1   Thumb Code    48  cmsis_os2.o(i.osKernelStart)
    osThreadNew                              0x08006709   Thumb Code   138  cmsis_os2.o(i.osThreadNew)
    pvPortMalloc                             0x08006f29   Thumb Code   216  heap_4.o(i.pvPortMalloc)
    pxPortInitialiseStack                    0x08007005   Thumb Code    40  port.o(i.pxPortInitialiseStack)
    uxListRemove                             0x08007031   Thumb Code    38  list.o(i.uxListRemove)
    vApplicationGetIdleTaskMemory            0x08007059   Thumb Code    16  cmsis_os2.o(i.vApplicationGetIdleTaskMemory)
    vApplicationGetTimerTaskMemory           0x0800706d   Thumb Code    18  cmsis_os2.o(i.vApplicationGetTimerTaskMemory)
    vListInitialise                          0x08007085   Thumb Code    22  list.o(i.vListInitialise)
    vListInitialiseItem                      0x0800709b   Thumb Code     6  list.o(i.vListInitialiseItem)
    vListInsert                              0x080070a1   Thumb Code    48  list.o(i.vListInsert)
    vListInsertEnd                           0x080070d1   Thumb Code    24  list.o(i.vListInsertEnd)
    vPortEnterCritical                       0x080070e9   Thumb Code    54  port.o(i.vPortEnterCritical)
    vPortExitCritical                        0x08007129   Thumb Code    34  port.o(i.vPortExitCritical)
    vPortFree                                0x08007151   Thumb Code    94  heap_4.o(i.vPortFree)
    vPortSetupTimerInterrupt                 0x080071b5   Thumb Code    32  port.o(i.vPortSetupTimerInterrupt)
    vPortValidateInterruptPriority           0x080071d9   Thumb Code    74  port.o(i.vPortValidateInterruptPriority)
    vQueueAddToRegistry                      0x0800722d   Thumb Code    34  queue.o(i.vQueueAddToRegistry)
    vQueueWaitForMessageRestricted           0x08007255   Thumb Code    68  queue.o(i.vQueueWaitForMessageRestricted)
    vTaskDelay                               0x08007299   Thumb Code    66  tasks.o(i.vTaskDelay)
    vTaskInternalSetTimeOutState             0x080072e5   Thumb Code    12  tasks.o(i.vTaskInternalSetTimeOutState)
    vTaskMissedYield                         0x080072f5   Thumb Code     8  tasks.o(i.vTaskMissedYield)
    vTaskPlaceOnEventList                    0x08007301   Thumb Code    44  tasks.o(i.vTaskPlaceOnEventList)
    vTaskPlaceOnEventListRestricted          0x08007331   Thumb Code    52  tasks.o(i.vTaskPlaceOnEventListRestricted)
    vTaskStartScheduler                      0x08007369   Thumb Code   118  tasks.o(i.vTaskStartScheduler)
    vTaskSuspendAll                          0x080073f1   Thumb Code    10  tasks.o(i.vTaskSuspendAll)
    vTaskSwitchContext                       0x08007401   Thumb Code    90  tasks.o(i.vTaskSwitchContext)
    vofa_Task                                0x08007465   Thumb Code    42  my_task.o(i.vofa_Task)
    xPortStartScheduler                      0x080074a9   Thumb Code   222  port.o(i.xPortStartScheduler)
    xPortSysTickHandler                      0x0800759d   Thumb Code    38  port.o(i.xPortSysTickHandler)
    xQueueGenericCreateStatic                0x080075c9   Thumb Code   102  queue.o(i.xQueueGenericCreateStatic)
    xQueueGenericReset                       0x08007631   Thumb Code   132  queue.o(i.xQueueGenericReset)
    xQueueGenericSend                        0x080076b9   Thumb Code   346  queue.o(i.xQueueGenericSend)
    xQueueGenericSendFromISR                 0x08007819   Thumb Code   190  queue.o(i.xQueueGenericSendFromISR)
    xQueueReceive                            0x080078d9   Thumb Code   308  queue.o(i.xQueueReceive)
    xTaskCheckForTimeOut                     0x08007a11   Thumb Code   112  tasks.o(i.xTaskCheckForTimeOut)
    xTaskCreate                              0x08007a85   Thumb Code    90  tasks.o(i.xTaskCreate)
    xTaskCreateStatic                        0x08007adf   Thumb Code    86  tasks.o(i.xTaskCreateStatic)
    xTaskGetSchedulerState                   0x08007b35   Thumb Code    22  tasks.o(i.xTaskGetSchedulerState)
    xTaskGetTickCount                        0x08007b51   Thumb Code     6  tasks.o(i.xTaskGetTickCount)
    xTaskIncrementTick                       0x08007b5d   Thumb Code   192  tasks.o(i.xTaskIncrementTick)
    xTaskPriorityDisinherit                  0x08007c25   Thumb Code   118  tasks.o(i.xTaskPriorityDisinherit)
    xTaskRemoveFromEventList                 0x08007ca5   Thumb Code    98  tasks.o(i.xTaskRemoveFromEventList)
    xTaskResumeAll                           0x08007d15   Thumb Code   182  tasks.o(i.xTaskResumeAll)
    xTimerCreateTimerTask                    0x08007dd9   Thumb Code    78  timers.o(i.xTimerCreateTimerTask)
    xTimerGenericCommand                     0x08007e39   Thumb Code    98  timers.o(i.xTimerGenericCommand)
    _get_lc_numeric                          0x08007ea1   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __fpl_dretinf                            0x08007ecd   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_f2d                              0x08007ed9   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08007ed9   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x08007f2f   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x08007fbb   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08007fc3   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08007fc3   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    _printf_fp_dec                           0x08007fc5   Thumb Code     4  printf1.o(x$fpl$printf1)
    __I$use$fp                               0x08007fc8   Number         0  usenofp.o(x$fpl$usenofp)
    afont16x8                                0x080085b8   Data           8  font.o(.constdata)
    font16x16                                0x08008650   Data          16  font.o(.constdata)
    defaultTask_attributes                   0x08008660   Data          36  freertos.o(.constdata)
    LED_attributes                           0x08008684   Data          36  freertos.o(.constdata)
    encoder_attributes                       0x080086a8   Data          36  freertos.o(.constdata)
    main_attributes                          0x080086cc   Data          36  freertos.o(.constdata)
    vofa_attributes                          0x080086f0   Data          36  freertos.o(.constdata)
    oled_attributes                          0x08008714   Data          36  freertos.o(.constdata)
    draw_attributes                          0x08008738   Data          36  freertos.o(.constdata)
    AHBPrescTable                            0x0800875c   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x0800876c   Data           8  system_stm32f4xx.o(.constdata)
    Region$$Table$$Base                      0x08008848   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08008868   Number         0  anon$$obj.o(Region$$Table)
    SHT$$INIT_ARRAY$$Base                    0x08008884   Number         0  global_variable.o(.init_array)
    SHT$$INIT_ARRAY$$Limit                   0x08008888   Number         0  init_aeabi.o(.init_array)
    defaultTaskHandle                        0x20000014   Data           4  freertos.o(.data)
    LEDHandle                                0x20000018   Data           4  freertos.o(.data)
    encoderHandle                            0x2000001c   Data           4  freertos.o(.data)
    mainHandle                               0x20000020   Data           4  freertos.o(.data)
    vofaHandle                               0x20000024   Data           4  freertos.o(.data)
    oledHandle                               0x20000028   Data           4  freertos.o(.data)
    drawHandle                               0x2000002c   Data           4  freertos.o(.data)
    angle_target                             0x20000030   Data           4  global_variable.o(.data)
    distance_target                          0x20000034   Data           4  global_variable.o(.data)
    const_zero                               0x20000038   Data           4  global_variable.o(.data)
    purple                                   0x2000003c   Data           8  global_variable.o(.data)
    target                                   0x20000044   Data           8  global_variable.o(.data)
    tick_ms                                  0x2000004c   Data           4  global_variable.o(.data)
    draw_task_state                          0x20000050   Data           4  global_variable.o(.data)
    black                                    0x20000054   Data           8  global_variable.o(.data)
    SystemCoreClock                          0x20000068   Data           4  system_stm32f4xx.o(.data)
    uwTickFreq                               0x2000006c   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000070   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x20000074   Data           4  stm32f4xx_hal.o(.data)
    pxCurrentTCB                             0x200000a8   Data           4  tasks.o(.data)
    LEDBuffer                                0x20000108   Data         512  freertos.o(.bss)
    LEDControlBlock                          0x20000308   Data          92  freertos.o(.bss)
    encoderBuffer                            0x20000364   Data        1024  freertos.o(.bss)
    encoderControlBlock                      0x20000764   Data          92  freertos.o(.bss)
    mainBuffer                               0x200007c0   Data        2048  freertos.o(.bss)
    mainControlBlock                         0x20000fc0   Data          92  freertos.o(.bss)
    vofaBuffer                               0x2000101c   Data        1024  freertos.o(.bss)
    vofaControlBlock                         0x2000141c   Data          92  freertos.o(.bss)
    oledBuffer                               0x20001478   Data        2048  freertos.o(.bss)
    oledControlBlock                         0x20001c78   Data          92  freertos.o(.bss)
    drawBuffer                               0x20001cd4   Data        2048  freertos.o(.bss)
    drawControlBlock                         0x200024d4   Data          92  freertos.o(.bss)
    imu                                      0x20002530   Data          60  global_variable.o(.bss)
    motor                                    0x2000256c   Data         108  global_variable.o(.bss)
    truck                                    0x200025d8   Data          20  global_variable.o(.bss)
    angle                                    0x200025ec   Data          64  global_variable.o(.bss)
    Velocity_L                               0x2000262c   Data          64  global_variable.o(.bss)
    Velocity_R                               0x2000266c   Data          64  global_variable.o(.bss)
    distance                                 0x200026ac   Data          64  global_variable.o(.bss)
    laser_x                                  0x200026ec   Data          64  global_variable.o(.bss)
    laser_y                                  0x2000272c   Data          64  global_variable.o(.bss)
    track                                    0x2000276c   Data          64  global_variable.o(.bss)
    camera                                   0x200027ac   Data          24  global_variable.o(.bss)
    debug                                    0x200027c4   Data          36  global_variable.o(.bss)
    key                                      0x200027e8   Data          32  global_variable.o(.bss)
    stepping_motor_up                        0x20002808   Data          20  global_variable.o(.bss)
    stepping_motor_down                      0x2000281c   Data          20  global_variable.o(.bss)
    hi2c1                                    0x20002830   Data          84  i2c.o(.bss)
    OLED_GRAM                                0x200029a5   Data        1024  oled.o(.bss)
    htim14                                   0x20002da8   Data          72  stm32f4xx_hal_timebase_tim.o(.bss)
    htim1                                    0x20002df0   Data          72  tim.o(.bss)
    htim2                                    0x20002e38   Data          72  tim.o(.bss)
    htim3                                    0x20002e80   Data          72  tim.o(.bss)
    htim6                                    0x20002ec8   Data          72  tim.o(.bss)
    huart1                                   0x20002f10   Data          72  usart.o(.bss)
    huart2                                   0x20002f58   Data          72  usart.o(.bss)
    huart3                                   0x20002fa0   Data          72  usart.o(.bss)
    huart6                                   0x20002fe8   Data          72  usart.o(.bss)
    hdma_usart1_rx                           0x20003030   Data          96  usart.o(.bss)
    hdma_usart1_tx                           0x20003090   Data          96  usart.o(.bss)
    hdma_usart2_rx                           0x200030f0   Data          96  usart.o(.bss)
    hdma_usart2_tx                           0x20003150   Data          96  usart.o(.bss)
    hdma_usart3_rx                           0x200031b0   Data          96  usart.o(.bss)
    hdma_usart3_tx                           0x20003210   Data          96  usart.o(.bss)
    hdma_usart6_rx                           0x20003270   Data          96  usart.o(.bss)
    hdma_usart6_tx                           0x200032d0   Data          96  usart.o(.bss)
    xQueueRegistry                           0x200075e8   Data          64  queue.o(.bss)
    __libspace_start                         0x20007c04   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20007c64   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00008980, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Base: 0x08000000, Size: 0x00008888, Max: 0x00080000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x00000188   Data   RO         6857    RESET               startup_stm32f407xx.o
    0x08000188   0x00000008   Code   RO         6913  * !!!main             c_w.l(__main.o)
    0x08000190   0x00000034   Code   RO         7177    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x0000001a   Code   RO         7179    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x00000002   PAD
    0x080001e0   0x0000001c   Code   RO         7181    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x00000000   Code   RO         6896    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001fc   0x00000006   Code   RO         6895    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000202   0x00000006   Code   RO         6894    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000208   0x00000004   Code   RO         6962    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x0800020c   0x00000002   Code   RO         7103    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800020e   0x00000004   Code   RO         6965    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000212   0x00000000   Code   RO         6968    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000212   0x00000000   Code   RO         6971    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000212   0x00000000   Code   RO         6973    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000212   0x00000000   Code   RO         6975    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000212   0x00000006   Code   RO         6976    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000218   0x00000000   Code   RO         6978    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000218   0x00000000   Code   RO         6980    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000218   0x00000000   Code   RO         6982    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000218   0x0000000a   Code   RO         6983    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x08000222   0x00000000   Code   RO         6984    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000222   0x00000000   Code   RO         6986    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000222   0x00000000   Code   RO         6988    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000222   0x00000000   Code   RO         6990    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000222   0x00000000   Code   RO         6992    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000222   0x00000000   Code   RO         6994    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000222   0x00000000   Code   RO         6996    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000222   0x00000000   Code   RO         6998    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000222   0x00000000   Code   RO         7002    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000222   0x00000000   Code   RO         7004    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000222   0x00000000   Code   RO         7006    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000222   0x00000004   Code   RO         7007    .ARM.Collect$$libinit$$00000031  c_w.l(libinit2.o)
    0x08000226   0x00000000   Code   RO         7008    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000226   0x00000002   Code   RO         7009    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000228   0x00000002   Code   RO         7144    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x0800022a   0x00000000   Code   RO         7160    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x0800022a   0x00000000   Code   RO         7162    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x0800022a   0x00000000   Code   RO         7165    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x0800022a   0x00000000   Code   RO         7168    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x0800022a   0x00000000   Code   RO         7170    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x0800022a   0x00000000   Code   RO         7173    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x0800022a   0x00000002   Code   RO         7174    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x0800022c   0x00000000   Code   RO         6949    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x0800022c   0x00000000   Code   RO         7018    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x0800022c   0x00000006   Code   RO         7030    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000232   0x00000000   Code   RO         7020    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000232   0x00000004   Code   RO         7021    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000236   0x00000000   Code   RO         7023    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000236   0x00000008   Code   RO         7024    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800023e   0x00000002   Code   RO         7106    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000240   0x00000000   Code   RO         7126    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000240   0x00000004   Code   RO         7127    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000244   0x00000006   Code   RO         7128    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x0800024a   0x00000002   PAD
    0x0800024c   0x000000be   Code   RO         5865    .emb_text           port.o
    0x0800030a   0x00000002   PAD
    0x0800030c   0x00000040   Code   RO         6858    .text               startup_stm32f407xx.o
    0x0800034c   0x000000ee   Code   RO         6864    .text               c_w.l(lludivv7m.o)
    0x0800043a   0x00000002   PAD
    0x0800043c   0x00000028   Code   RO         6868    .text               c_w.l(noretval__2sprintf.o)
    0x08000464   0x00000078   Code   RO         6872    .text               c_w.l(_printf_dec.o)
    0x080004dc   0x0000010e   Code   RO         6882    .text               c_w.l(__printf_wp.o)
    0x080005ea   0x00000058   Code   RO         6897    .text               c_w.l(memcmp.o)
    0x08000642   0x0000003e   Code   RO         6899    .text               c_w.l(strlen.o)
    0x08000680   0x0000008a   Code   RO         6901    .text               c_w.l(rt_memcpy_v6.o)
    0x0800070a   0x00000010   Code   RO         6905    .text               c_w.l(aeabi_memset.o)
    0x0800071a   0x00000044   Code   RO         6907    .text               c_w.l(rt_memclr.o)
    0x0800075e   0x0000004e   Code   RO         6909    .text               c_w.l(rt_memclr_w.o)
    0x080007ac   0x00000006   Code   RO         6911    .text               c_w.l(heapauxi.o)
    0x080007b2   0x00000002   PAD
    0x080007b4   0x00000024   Code   RO         6918    .text               c_w.l(init_aeabi.o)
    0x080007d8   0x000000b2   Code   RO         6954    .text               c_w.l(_printf_intcommon.o)
    0x0800088a   0x0000041e   Code   RO         6956    .text               c_w.l(_printf_fp_dec.o)
    0x08000ca8   0x00000030   Code   RO         6958    .text               c_w.l(_printf_char_common.o)
    0x08000cd8   0x0000000a   Code   RO         6960    .text               c_w.l(_sputc.o)
    0x08000ce2   0x00000064   Code   RO         6963    .text               c_w.l(rt_memcpy_w.o)
    0x08000d46   0x00000002   PAD
    0x08000d48   0x00000008   Code   RO         7037    .text               c_w.l(rt_locale_intlibspace.o)
    0x08000d50   0x0000008a   Code   RO         7039    .text               c_w.l(lludiv10.o)
    0x08000dda   0x00000002   PAD
    0x08000ddc   0x00000080   Code   RO         7041    .text               c_w.l(_printf_fp_infnan.o)
    0x08000e5c   0x000000e4   Code   RO         7045    .text               c_w.l(bigflt0.o)
    0x08000f40   0x00000008   Code   RO         7081    .text               c_w.l(libspace.o)
    0x08000f48   0x0000004a   Code   RO         7084    .text               c_w.l(sys_stackheap_outer.o)
    0x08000f92   0x00000012   Code   RO         7086    .text               c_w.l(exit.o)
    0x08000fa4   0x00000080   Code   RO         7092    .text               c_w.l(strcmpv7m.o)
    0x08001024   0x0000000c   Code   RO         7120    .text               c_w.l(sys_exit.o)
    0x08001030   0x00000002   Code   RO         7122    .text               c_w.l(use_no_semi.o)
    0x08001032   0x00000000   Code   RO         7124    .text               c_w.l(indicate_semi.o)
    0x08001032   0x0000003e   Code   RO         7048    CL$$btod_d2e        c_w.l(btod.o)
    0x08001070   0x00000046   Code   RO         7050    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x080010b6   0x00000060   Code   RO         7049    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08001116   0x00000338   Code   RO         7058    CL$$btod_div_common  c_w.l(btod.o)
    0x0800144e   0x000000dc   Code   RO         7055    CL$$btod_e2e        c_w.l(btod.o)
    0x0800152a   0x0000002a   Code   RO         7052    CL$$btod_ediv       c_w.l(btod.o)
    0x08001554   0x0000002a   Code   RO         7051    CL$$btod_emul       c_w.l(btod.o)
    0x0800157e   0x00000244   Code   RO         7057    CL$$btod_mult_common  c_w.l(btod.o)
    0x080017c2   0x00000002   Code   RO         1602    i.BusFault_Handler  stm32f4xx_it.o
    0x080017c4   0x0000000c   Code   RO         1603    i.DMA1_Stream1_IRQHandler  stm32f4xx_it.o
    0x080017d0   0x0000000c   Code   RO         1604    i.DMA1_Stream3_IRQHandler  stm32f4xx_it.o
    0x080017dc   0x0000000c   Code   RO         1605    i.DMA1_Stream5_IRQHandler  stm32f4xx_it.o
    0x080017e8   0x0000000c   Code   RO         1606    i.DMA1_Stream6_IRQHandler  stm32f4xx_it.o
    0x080017f4   0x0000000c   Code   RO         1607    i.DMA2_Stream1_IRQHandler  stm32f4xx_it.o
    0x08001800   0x0000000c   Code   RO         1608    i.DMA2_Stream2_IRQHandler  stm32f4xx_it.o
    0x0800180c   0x0000000c   Code   RO         1609    i.DMA2_Stream6_IRQHandler  stm32f4xx_it.o
    0x08001818   0x0000000c   Code   RO         1610    i.DMA2_Stream7_IRQHandler  stm32f4xx_it.o
    0x08001824   0x00000028   Code   RO         2498    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x0800184c   0x00000054   Code   RO         2499    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x080018a0   0x00000028   Code   RO         2500    i.DMA_SetConfig     stm32f4xx_hal_dma.o
    0x080018c8   0x00000002   Code   RO         1611    i.DebugMon_Handler  stm32f4xx_it.o
    0x080018ca   0x00000002   PAD
    0x080018cc   0x0000004c   Code   RO         1184    i.Encoder_Task      my_task.o
    0x08001918   0x00000004   Code   RO         1035    i.Error_Handler     main.o
    0x0800191c   0x00000092   Code   RO         2501    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x080019ae   0x00000024   Code   RO         2502    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x080019d2   0x00000006   Code   RO         2505    i.HAL_DMA_GetState  stm32f4xx_hal_dma.o
    0x080019d8   0x000001a0   Code   RO         2506    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x08001b78   0x000000d4   Code   RO         2507    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x08001c4c   0x0000006e   Code   RO         2511    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x08001cba   0x00000002   PAD
    0x08001cbc   0x00000024   Code   RO         2166    i.HAL_Delay         stm32f4xx_hal.o
    0x08001ce0   0x000001f0   Code   RO         2916    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x08001ed0   0x00000010   Code   RO         2919    i.HAL_GPIO_TogglePin  stm32f4xx_hal_gpio.o
    0x08001ee0   0x0000000a   Code   RO         2920    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08001eea   0x00000002   PAD
    0x08001eec   0x0000000c   Code   RO         2172    i.HAL_GetTick       stm32f4xx_hal.o
    0x08001ef8   0x00000002   Code   RO         2979    i.HAL_I2C_AbortCpltCallback  stm32f4xx_hal_i2c.o
    0x08001efa   0x00000002   Code   RO         2980    i.HAL_I2C_AddrCallback  stm32f4xx_hal_i2c.o
    0x08001efc   0x000000ba   Code   RO         2983    i.HAL_I2C_ER_IRQHandler  stm32f4xx_hal_i2c.o
    0x08001fb6   0x00000230   Code   RO         2984    i.HAL_I2C_EV_IRQHandler  stm32f4xx_hal_i2c.o
    0x080021e6   0x00000002   Code   RO         2986    i.HAL_I2C_ErrorCallback  stm32f4xx_hal_i2c.o
    0x080021e8   0x00000188   Code   RO         2990    i.HAL_I2C_Init      stm32f4xx_hal_i2c.o
    0x08002370   0x00000002   Code   RO         2992    i.HAL_I2C_ListenCpltCallback  stm32f4xx_hal_i2c.o
    0x08002372   0x00000002   Code   RO         2993    i.HAL_I2C_MasterRxCpltCallback  stm32f4xx_hal_i2c.o
    0x08002374   0x00000002   Code   RO         2994    i.HAL_I2C_MasterTxCpltCallback  stm32f4xx_hal_i2c.o
    0x08002376   0x00000002   PAD
    0x08002378   0x0000012c   Code   RO         3003    i.HAL_I2C_Master_Transmit  stm32f4xx_hal_i2c.o
    0x080024a4   0x00000002   Code   RO         3006    i.HAL_I2C_MemRxCpltCallback  stm32f4xx_hal_i2c.o
    0x080024a6   0x00000002   Code   RO         3007    i.HAL_I2C_MemTxCpltCallback  stm32f4xx_hal_i2c.o
    0x080024a8   0x00000088   Code   RO          892    i.HAL_I2C_MspInit   i2c.o
    0x08002530   0x00000002   Code   RO         3016    i.HAL_I2C_SlaveRxCpltCallback  stm32f4xx_hal_i2c.o
    0x08002532   0x00000002   Code   RO         3017    i.HAL_I2C_SlaveTxCpltCallback  stm32f4xx_hal_i2c.o
    0x08002534   0x00000010   Code   RO         2178    i.HAL_IncTick       stm32f4xx_hal.o
    0x08002544   0x00000034   Code   RO         2179    i.HAL_Init          stm32f4xx_hal.o
    0x08002578   0x00000098   Code   RO         1563    i.HAL_InitTick      stm32f4xx_hal_timebase_tim.o
    0x08002610   0x00000038   Code   RO         1539    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08002648   0x0000001a   Code   RO         2353    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08002662   0x00000002   PAD
    0x08002664   0x00000040   Code   RO         2359    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x080026a4   0x00000024   Code   RO         2360    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x080026c8   0x00000134   Code   RO         3609    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x080027fc   0x00000040   Code   RO         3613    i.HAL_RCC_GetClockConfig  stm32f4xx_hal_rcc.o
    0x0800283c   0x00000020   Code   RO         3616    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x0800285c   0x00000020   Code   RO         3617    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x0800287c   0x00000060   Code   RO         3618    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x080028dc   0x0000036c   Code   RO         3621    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08002c48   0x00000002   Code   RO         4470    i.HAL_TIMEx_BreakCallback  stm32f4xx_hal_tim_ex.o
    0x08002c4a   0x00000002   Code   RO         4471    i.HAL_TIMEx_CommutCallback  stm32f4xx_hal_tim_ex.o
    0x08002c4c   0x00000054   Code   RO         4473    i.HAL_TIMEx_ConfigBreakDeadTime  stm32f4xx_hal_tim_ex.o
    0x08002ca0   0x00000090   Code   RO         4489    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x08002d30   0x0000005a   Code   RO         3766    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x08002d8a   0x00000002   PAD
    0x08002d8c   0x0000003c   Code   RO         1795    i.HAL_TIM_Base_MspInit  tim.o
    0x08002dc8   0x00000080   Code   RO         3771    i.HAL_TIM_Base_Start_IT  stm32f4xx_hal_tim.o
    0x08002e48   0x000000a4   Code   RO         3787    i.HAL_TIM_Encoder_Init  stm32f4xx_hal_tim.o
    0x08002eec   0x000000c8   Code   RO         1797    i.HAL_TIM_Encoder_MspInit  tim.o
    0x08002fb4   0x0000008e   Code   RO         3790    i.HAL_TIM_Encoder_Start  stm32f4xx_hal_tim.o
    0x08003042   0x00000002   Code   RO         3800    i.HAL_TIM_IC_CaptureCallback  stm32f4xx_hal_tim.o
    0x08003044   0x00000130   Code   RO         3814    i.HAL_TIM_IRQHandler  stm32f4xx_hal_tim.o
    0x08003174   0x0000007c   Code   RO         1798    i.HAL_TIM_MspPostInit  tim.o
    0x080031f0   0x00000002   Code   RO         3817    i.HAL_TIM_OC_DelayElapsedCallback  stm32f4xx_hal_tim.o
    0x080031f2   0x000000cc   Code   RO         3838    i.HAL_TIM_PWM_ConfigChannel  stm32f4xx_hal_tim.o
    0x080032be   0x0000005a   Code   RO         3841    i.HAL_TIM_PWM_Init  stm32f4xx_hal_tim.o
    0x08003318   0x00000028   Code   RO         1800    i.HAL_TIM_PWM_MspInit  tim.o
    0x08003340   0x00000002   Code   RO         3844    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f4xx_hal_tim.o
    0x08003342   0x00000002   PAD
    0x08003344   0x000000c8   Code   RO         3846    i.HAL_TIM_PWM_Start  stm32f4xx_hal_tim.o
    0x0800340c   0x00000028   Code   RO         1036    i.HAL_TIM_PeriodElapsedCallback  main.o
    0x08003434   0x00000002   Code   RO         3857    i.HAL_TIM_TriggerCallback  stm32f4xx_hal_tim.o
    0x08003436   0x0000004e   Code   RO         4747    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x08003484   0x00000150   Code   RO          366    i.HAL_UARTEx_RxEventCallback  callback.o
    0x080035d4   0x00000002   Code   RO         4763    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x080035d6   0x00000002   PAD
    0x080035d8   0x00000270   Code   RO         4766    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x08003848   0x00000064   Code   RO         4767    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x080038ac   0x00000374   Code   RO         1916    i.HAL_UART_MspInit  usart.o
    0x08003c20   0x00000002   Code   RO         4773    i.HAL_UART_RxCpltCallback  stm32f4xx_hal_uart.o
    0x08003c22   0x00000002   Code   RO         4774    i.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x08003c24   0x00000078   Code   RO         4776    i.HAL_UART_Transmit_DMA  stm32f4xx_hal_uart.o
    0x08003c9c   0x00000002   Code   RO         4778    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08003c9e   0x00000002   Code   RO         4779    i.HAL_UART_TxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x08003ca0   0x00000002   Code   RO         1612    i.HardFault_Handler  stm32f4xx_it.o
    0x08003ca2   0x00000002   PAD
    0x08003ca4   0x0000000c   Code   RO         1613    i.I2C1_ER_IRQHandler  stm32f4xx_it.o
    0x08003cb0   0x0000000c   Code   RO         1614    i.I2C1_EV_IRQHandler  stm32f4xx_it.o
    0x08003cbc   0x000000bc   Code   RO         3028    i.I2C_DMAAbort      stm32f4xx_hal_i2c.o
    0x08003d78   0x00000010   Code   RO         3031    i.I2C_Flush_DR      stm32f4xx_hal_i2c.o
    0x08003d88   0x00000158   Code   RO         3032    i.I2C_ITError       stm32f4xx_hal_i2c.o
    0x08003ee0   0x0000002e   Code   RO         3033    i.I2C_IsAcknowledgeFailed  stm32f4xx_hal_i2c.o
    0x08003f0e   0x000000da   Code   RO         3034    i.I2C_MasterReceive_BTF  stm32f4xx_hal_i2c.o
    0x08003fe8   0x000000f4   Code   RO         3035    i.I2C_MasterReceive_RXNE  stm32f4xx_hal_i2c.o
    0x080040dc   0x0000009c   Code   RO         3037    i.I2C_MasterRequestWrite  stm32f4xx_hal_i2c.o
    0x08004178   0x00000082   Code   RO         3038    i.I2C_MasterTransmit_BTF  stm32f4xx_hal_i2c.o
    0x080041fa   0x00000002   PAD
    0x080041fc   0x000000b6   Code   RO         3039    i.I2C_MasterTransmit_TXE  stm32f4xx_hal_i2c.o
    0x080042b2   0x00000002   PAD
    0x080042b4   0x00000118   Code   RO         3040    i.I2C_Master_ADDR   stm32f4xx_hal_i2c.o
    0x080043cc   0x0000008c   Code   RO         3041    i.I2C_Master_SB     stm32f4xx_hal_i2c.o
    0x08004458   0x000000a8   Code   RO         3042    i.I2C_MemoryTransmit_TXE_BTF  stm32f4xx_hal_i2c.o
    0x08004500   0x00000046   Code   RO         3045    i.I2C_Slave_ADDR    stm32f4xx_hal_i2c.o
    0x08004546   0x00000002   PAD
    0x08004548   0x00000090   Code   RO         3046    i.I2C_Slave_AF      stm32f4xx_hal_i2c.o
    0x080045d8   0x0000015c   Code   RO         3047    i.I2C_Slave_STOPF   stm32f4xx_hal_i2c.o
    0x08004734   0x00000056   Code   RO         3048    i.I2C_WaitOnBTFFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x0800478a   0x00000002   PAD
    0x0800478c   0x00000090   Code   RO         3049    i.I2C_WaitOnFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x0800481c   0x000000bc   Code   RO         3050    i.I2C_WaitOnMasterAddressFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x080048d8   0x00000056   Code   RO         3052    i.I2C_WaitOnTXEFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x0800492e   0x00000002   PAD
    0x08004930   0x00000020   Code   RO         1185    i.LED_Task          my_task.o
    0x08004950   0x000000ac   Code   RO          391    i.MX_DMA_Init       dma.o
    0x080049fc   0x0000008c   Code   RO          699    i.MX_FREERTOS_Init  freertos.o
    0x08004a88   0x00000144   Code   RO          864    i.MX_GPIO_Init      gpio.o
    0x08004bcc   0x00000040   Code   RO          893    i.MX_I2C1_Init      i2c.o
    0x08004c0c   0x000000b0   Code   RO         1801    i.MX_TIM1_Init      tim.o
    0x08004cbc   0x00000068   Code   RO         1802    i.MX_TIM2_Init      tim.o
    0x08004d24   0x0000006c   Code   RO         1803    i.MX_TIM3_Init      tim.o
    0x08004d90   0x00000044   Code   RO         1804    i.MX_TIM6_Init      tim.o
    0x08004dd4   0x00000038   Code   RO         1917    i.MX_USART1_UART_Init  usart.o
    0x08004e0c   0x00000038   Code   RO         1918    i.MX_USART2_UART_Init  usart.o
    0x08004e44   0x00000038   Code   RO         1919    i.MX_USART3_UART_Init  usart.o
    0x08004e7c   0x00000038   Code   RO         1920    i.MX_USART6_UART_Init  usart.o
    0x08004eb4   0x00000002   Code   RO         1615    i.MemManage_Handler  stm32f4xx_it.o
    0x08004eb6   0x00000002   Code   RO         1616    i.NMI_Handler       stm32f4xx_it.o
    0x08004eb8   0x00000050   Code   RO         1186    i.OLED_Task         my_task.o
    0x08004f08   0x00000008   Code   RO          701    i.StartDefaultTask  freertos.o
    0x08004f10   0x0000001a   Code   RO         5103    i.SysTick_Handler   cmsis_os2.o
    0x08004f2a   0x00000002   PAD
    0x08004f2c   0x00000010   Code   RO         1757    i.SystemInit        system_stm32f4xx.o
    0x08004f3c   0x0000000c   Code   RO         1617    i.TIM6_DAC_IRQHandler  stm32f4xx_it.o
    0x08004f48   0x0000000c   Code   RO         1618    i.TIM8_TRG_COM_TIM14_IRQHandler  stm32f4xx_it.o
    0x08004f54   0x000000d8   Code   RO         3859    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x0800502c   0x0000001a   Code   RO         3860    i.TIM_CCxChannelCmd  stm32f4xx_hal_tim.o
    0x08005046   0x00000002   PAD
    0x08005048   0x00000060   Code   RO         3872    i.TIM_OC1_SetConfig  stm32f4xx_hal_tim.o
    0x080050a8   0x0000006c   Code   RO         3873    i.TIM_OC2_SetConfig  stm32f4xx_hal_tim.o
    0x08005114   0x00000068   Code   RO         3874    i.TIM_OC3_SetConfig  stm32f4xx_hal_tim.o
    0x0800517c   0x00000050   Code   RO         3875    i.TIM_OC4_SetConfig  stm32f4xx_hal_tim.o
    0x080051cc   0x00000010   Code   RO         4780    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x080051dc   0x0000004a   Code   RO         4781    i.UART_DMAError     stm32f4xx_hal_uart.o
    0x08005226   0x00000086   Code   RO         4782    i.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x080052ac   0x0000001e   Code   RO         4784    i.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x080052ca   0x00000042   Code   RO         4786    i.UART_DMATransmitCplt  stm32f4xx_hal_uart.o
    0x0800530c   0x0000000a   Code   RO         4788    i.UART_DMATxHalfCplt  stm32f4xx_hal_uart.o
    0x08005316   0x0000004e   Code   RO         4790    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x08005364   0x0000001c   Code   RO         4791    i.UART_EndTxTransfer  stm32f4xx_hal_uart.o
    0x08005380   0x000000c2   Code   RO         4792    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x08005442   0x00000002   PAD
    0x08005444   0x0000010c   Code   RO         4793    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x08005550   0x00000090   Code   RO         4794    i.UART_Start_Receive_DMA  stm32f4xx_hal_uart.o
    0x080055e0   0x0000000c   Code   RO         1619    i.USART1_IRQHandler  stm32f4xx_it.o
    0x080055ec   0x0000000c   Code   RO         1620    i.USART2_IRQHandler  stm32f4xx_it.o
    0x080055f8   0x0000000c   Code   RO         1621    i.USART3_IRQHandler  stm32f4xx_it.o
    0x08005604   0x0000000c   Code   RO         1622    i.USART6_IRQHandler  stm32f4xx_it.o
    0x08005610   0x00000002   Code   RO         1623    i.UsageFault_Handler  stm32f4xx_it.o
    0x08005612   0x00000002   PAD
    0x08005614   0x00000090   Code   RO          818    i._Z11system_initv  global_variable.o
    0x080056a4   0x00000010   Code   RO         1245    i._Z12OLED_SendCmdh  oled.o
    0x080056b4   0x00000048   Code   RO         1246    i._Z12OLED_SetBitshhh14OLED_ColorMode  oled.o
    0x080056fc   0x00000010   Code   RO         1249    i._Z13OLED_NewFramev  oled.o
    0x0800570c   0x00000088   Code   RO         1250    i._Z13OLED_SetBlockhhPKhhh14OLED_ColorMode  oled.o
    0x08005794   0x00000048   Code   RO         1253    i._Z14OLED_ShowFramev  oled.o
    0x080057dc   0x000000b8   Code   RO         1258    i._Z16OLED_PrintStringhhPcPK4Font14OLED_ColorMode  oled.o
    0x08005894   0x00000032   Code   RO         1259    i._Z16_OLED_GetUTF8LenPc  oled.o
    0x080058c6   0x00000068   Code   RO         1261    i._Z17OLED_SetBits_Finehhhh14OLED_ColorMode  oled.o
    0x0800592e   0x00000002   PAD
    0x08005930   0x00000048   Code   RO         1262    i._Z17OLED_SetByte_Finehhhhh14OLED_ColorMode  oled.o
    0x08005978   0x00000094   Code   RO         1037    i._Z18SystemClock_Configv  main.o
    0x08005a0c   0x00000022   Code   RO         1265    i._Z19OLED_PrintASCIICharhhcPK9ASCIIFont14OLED_ColorMode  oled.o
    0x08005a2e   0x000000b6   Code   RO         1270    i._Z9OLED_Initv     oled.o
    0x08005ae4   0x0000001c   Code   RO         1271    i._Z9OLED_SendPhh   oled.o
    0x08005b00   0x000000f0   Code   RO         1461    i._ZN14PID_Controller10pid_figureEv  pid.o
    0x08005bf0   0x0000003c   Code   RO         1464    i._ZN14PID_Controller9dead_zoneEv  pid.o
    0x08005c2c   0x00000040   Code   RO         1465    i._ZN14PID_ControllerC1Efff8pid_modePfS1_S1_fbf  pid.o
    0x08005c6c   0x00000008   Code   RO         1006    i._ZN3KEYC1EP12GPIO_TypeDeft8KEY_Mode  key.o
    0x08005c74   0x000000a0   Code   RO          273    i._ZN3ZDT16position_controlEhththh  ZDT.o
    0x08005d14   0x0000002c   Code   RO          274    i._ZN3ZDT4initEv    ZDT.o
    0x08005d40   0x00000028   Code   RO          275    i._ZN3ZDT9deal_dataEv  ZDT.o
    0x08005d68   0x0000000a   Code   RO          276    i._ZN3ZDTC1EP20__UART_HandleTypeDefh  ZDT.o
    0x08005d72   0x00000002   PAD
    0x08005d74   0x00000114   Code   RO         1975    i._ZN4vofa13analysis_dataEv  vofa.o
    0x08005e88   0x0000001e   Code   RO         1976    i._ZN4vofa4initEv   vofa.o
    0x08005ea6   0x00000002   PAD
    0x08005ea8   0x0000003c   Code   RO         1978    i._ZN4vofa9read_dataEv  vofa.o
    0x08005ee4   0x00000004   Code   RO         1979    i._ZN4vofaC1EP20__UART_HandleTypeDef  vofa.o
    0x08005ee8   0x00000022   Code   RO         1885    i._ZN5TruckC1Ev     truck.o
    0x08005f0a   0x00000002   PAD
    0x08005f0c   0x000000fc   Code   RO         1119    i._ZN6TB661214encoder_updateEv  motor.o
    0x08006008   0x00000038   Code   RO         1120    i._ZN6TB66124initEv  motor.o
    0x08006040   0x00000016   Code   RO         1121    i._ZN6TB66128set_dutyEss  motor.o
    0x08006056   0x00000096   Code   RO         1122    i._ZN6TB66129set_duty1Es  motor.o
    0x080060ec   0x00000096   Code   RO         1123    i._ZN6TB66129set_duty2Es  motor.o
    0x08006182   0x00000052   Code   RO         1124    i._ZN6TB6612C1EP17TIM_HandleTypeDefjjP12GPIO_TypeDeftS3_tS3_tS3_tS1_S1_fffffbb  motor.o
    0x080061d4   0x0000005c   Code   RO          933    i._ZN6jy901s11sustain_yawEv  jy901s.o
    0x08006230   0x00000024   Code   RO          935    i._ZN6jy901s4initEv  jy901s.o
    0x08006254   0x0000000e   Code   RO          936    i._ZN6jy901s4zeroEv  jy901s.o
    0x08006262   0x00000002   PAD
    0x08006264   0x00000064   Code   RO          938    i._ZN6jy901s6updateEv  jy901s.o
    0x080062c8   0x00000044   Code   RO          941    i._ZN6jy901sC1EP20__UART_HandleTypeDef  jy901s.o
    0x0800630c   0x0000001e   Code   RO         1081    i._ZN7maixcam4initEv  maixcam.o
    0x0800632a   0x00000002   PAD
    0x0800632c   0x00000048   Code   RO         1082    i._ZN7maixcam6updateEv  maixcam.o
    0x08006374   0x00000004   Code   RO         1083    i._ZN7maixcamC1EP20__UART_HandleTypeDef  maixcam.o
    0x08006378   0x00000030   Code   RO         7079    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x080063a8   0x00000020   Code   RO         2366    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x080063c8   0x00000010   Code   RO         6921    i.__cxa_guard_acquire  cpprt_w.l(cxa_guard_acquire.o)
    0x080063d8   0x00000268   Code   RO          819    i.__sti___19_global_variable_cpp_f9d78f51  global_variable.o
    0x08006640   0x0000000e   Code   RO         6884    i._is_digit         c_w.l(__printf_wp.o)
    0x0800664e   0x00000002   PAD
    0x08006650   0x00000024   Code   RO         1187    i.draw_Task         my_task.o
    0x08006674   0x0000001a   Code   RO         1038    i.main              main.o
    0x0800668e   0x00000002   Code   RO         1188    i.main_Task         my_task.o
    0x08006690   0x0000001a   Code   RO         5105    i.osDelay           cmsis_os2.o
    0x080066aa   0x00000002   PAD
    0x080066ac   0x00000024   Code   RO         5119    i.osKernelInitialize  cmsis_os2.o
    0x080066d0   0x00000038   Code   RO         5122    i.osKernelStart     cmsis_os2.o
    0x08006708   0x0000008a   Code   RO         5164    i.osThreadNew       cmsis_os2.o
    0x08006792   0x00000002   PAD
    0x08006794   0x0000005c   Code   RO         6337    i.prvAddCurrentTaskToDelayedList  tasks.o
    0x080067f0   0x000000d0   Code   RO         6338    i.prvAddNewTaskToReadyList  tasks.o
    0x080068c0   0x00000058   Code   RO         6702    i.prvCheckForValidListAndQueue  timers.o
    0x08006918   0x00000026   Code   RO         5943    i.prvCopyDataFromQueue  queue.o
    0x0800693e   0x0000006c   Code   RO         5944    i.prvCopyDataToQueue  queue.o
    0x080069aa   0x00000034   Code   RO         6339    i.prvDeleteTCB      tasks.o
    0x080069de   0x00000002   PAD
    0x080069e0   0x0000004c   Code   RO         5798    i.prvHeapInit       heap_4.o
    0x08006a2c   0x00000060   Code   RO         6340    i.prvIdleTask       tasks.o
    0x08006a8c   0x00000022   Code   RO         5946    i.prvInitialiseNewQueue  queue.o
    0x08006aae   0x00000002   PAD
    0x08006ab0   0x000000b0   Code   RO         6341    i.prvInitialiseNewTask  tasks.o
    0x08006b60   0x0000004c   Code   RO         5799    i.prvInsertBlockIntoFreeList  heap_4.o
    0x08006bac   0x00000038   Code   RO         6704    i.prvInsertTimerInActiveList  timers.o
    0x08006be4   0x0000001c   Code   RO         5947    i.prvIsQueueEmpty   queue.o
    0x08006c00   0x000000fc   Code   RO         6705    i.prvProcessReceivedCommands  timers.o
    0x08006cfc   0x000000c0   Code   RO         6706    i.prvProcessTimerOrBlockTask  timers.o
    0x08006dbc   0x00000020   Code   RO         6343    i.prvResetNextTaskUnblockTime  tasks.o
    0x08006ddc   0x00000028   Code   RO         6707    i.prvSampleTimeNow  timers.o
    0x08006e04   0x0000006c   Code   RO         6708    i.prvSwitchTimerLists  timers.o
    0x08006e70   0x00000028   Code   RO         5866    i.prvTaskExitError  port.o
    0x08006e98   0x00000024   Code   RO         6709    i.prvTimerTask      timers.o
    0x08006ebc   0x0000006a   Code   RO         5948    i.prvUnlockQueue    queue.o
    0x08006f26   0x00000002   PAD
    0x08006f28   0x000000dc   Code   RO         5800    i.pvPortMalloc      heap_4.o
    0x08007004   0x0000002c   Code   RO         5867    i.pxPortInitialiseStack  port.o
    0x08007030   0x00000026   Code   RO         5759    i.uxListRemove      list.o
    0x08007056   0x00000002   PAD
    0x08007058   0x00000014   Code   RO         5176    i.vApplicationGetIdleTaskMemory  cmsis_os2.o
    0x0800706c   0x00000018   Code   RO         5177    i.vApplicationGetTimerTaskMemory  cmsis_os2.o
    0x08007084   0x00000016   Code   RO         5760    i.vListInitialise   list.o
    0x0800709a   0x00000006   Code   RO         5761    i.vListInitialiseItem  list.o
    0x080070a0   0x00000030   Code   RO         5762    i.vListInsert       list.o
    0x080070d0   0x00000018   Code   RO         5763    i.vListInsertEnd    list.o
    0x080070e8   0x00000040   Code   RO         5869    i.vPortEnterCritical  port.o
    0x08007128   0x00000028   Code   RO         5870    i.vPortExitCritical  port.o
    0x08007150   0x00000064   Code   RO         5801    i.vPortFree         heap_4.o
    0x080071b4   0x00000024   Code   RO         5871    i.vPortSetupTimerInterrupt  port.o
    0x080071d8   0x00000054   Code   RO         5872    i.vPortValidateInterruptPriority  port.o
    0x0800722c   0x00000028   Code   RO         5954    i.vQueueAddToRegistry  queue.o
    0x08007254   0x00000044   Code   RO         5958    i.vQueueWaitForMessageRestricted  queue.o
    0x08007298   0x0000004c   Code   RO         6356    i.vTaskDelay        tasks.o
    0x080072e4   0x00000010   Code   RO         6361    i.vTaskInternalSetTimeOutState  tasks.o
    0x080072f4   0x0000000c   Code   RO         6362    i.vTaskMissedYield  tasks.o
    0x08007300   0x00000030   Code   RO         6364    i.vTaskPlaceOnEventList  tasks.o
    0x08007330   0x00000038   Code   RO         6365    i.vTaskPlaceOnEventListRestricted  tasks.o
    0x08007368   0x00000088   Code   RO         6373    i.vTaskStartScheduler  tasks.o
    0x080073f0   0x00000010   Code   RO         6375    i.vTaskSuspendAll   tasks.o
    0x08007400   0x00000064   Code   RO         6376    i.vTaskSwitchContext  tasks.o
    0x08007464   0x00000044   Code   RO         1189    i.vofa_Task         my_task.o
    0x080074a8   0x000000f4   Code   RO         5873    i.xPortStartScheduler  port.o
    0x0800759c   0x0000002c   Code   RO         5874    i.xPortSysTickHandler  port.o
    0x080075c8   0x00000066   Code   RO         5964    i.xQueueGenericCreateStatic  queue.o
    0x0800762e   0x00000002   PAD
    0x08007630   0x00000088   Code   RO         5965    i.xQueueGenericReset  queue.o
    0x080076b8   0x00000160   Code   RO         5966    i.xQueueGenericSend  queue.o
    0x08007818   0x000000be   Code   RO         5967    i.xQueueGenericSendFromISR  queue.o
    0x080078d6   0x00000002   PAD
    0x080078d8   0x00000138   Code   RO         5976    i.xQueueReceive     queue.o
    0x08007a10   0x00000074   Code   RO         6378    i.xTaskCheckForTimeOut  tasks.o
    0x08007a84   0x0000005a   Code   RO         6379    i.xTaskCreate       tasks.o
    0x08007ade   0x00000056   Code   RO         6380    i.xTaskCreateStatic  tasks.o
    0x08007b34   0x0000001c   Code   RO         6384    i.xTaskGetSchedulerState  tasks.o
    0x08007b50   0x0000000c   Code   RO         6385    i.xTaskGetTickCount  tasks.o
    0x08007b5c   0x000000c8   Code   RO         6387    i.xTaskIncrementTick  tasks.o
    0x08007c24   0x00000080   Code   RO         6390    i.xTaskPriorityDisinherit  tasks.o
    0x08007ca4   0x00000070   Code   RO         6392    i.xTaskRemoveFromEventList  tasks.o
    0x08007d14   0x000000c4   Code   RO         6393    i.xTaskResumeAll    tasks.o
    0x08007dd8   0x00000060   Code   RO         6718    i.xTimerCreateTimerTask  timers.o
    0x08007e38   0x00000068   Code   RO         6719    i.xTimerGenericCommand  timers.o
    0x08007ea0   0x0000002c   Code   RO         7071    locale$$code        c_w.l(lc_numeric_c.o)
    0x08007ecc   0x0000000c   Code   RO         7012    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x08007ed8   0x00000056   Code   RO         6945    x$fpl$f2d           fz_wm.l(f2d.o)
    0x08007f2e   0x0000008c   Code   RO         7014    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x08007fba   0x0000000a   Code   RO         7075    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08007fc4   0x00000004   Code   RO         6947    x$fpl$printf1       fz_wm.l(printf1.o)
    0x08007fc8   0x00000000   Code   RO         7016    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x08007fc8   0x000005f0   Data   RO          525    .constdata          font.o
    0x080085b8   0x00000008   Data   RO          526    .constdata          font.o
    0x080085c0   0x00000090   Data   RO          529    .constdata          font.o
    0x08008650   0x00000010   Data   RO          530    .constdata          font.o
    0x08008660   0x000000fc   Data   RO          717    .constdata          freertos.o
    0x0800875c   0x00000010   Data   RO         1758    .constdata          system_stm32f4xx.o
    0x0800876c   0x00000008   Data   RO         1759    .constdata          system_stm32f4xx.o
    0x08008774   0x00000008   Data   RO         2513    .constdata          stm32f4xx_hal_dma.o
    0x0800877c   0x00000094   Data   RO         7046    .constdata          c_w.l(bigflt0.o)
    0x08008810   0x00000035   Data   RO          718    .conststring        freertos.o
    0x08008845   0x00000003   PAD
    0x08008848   0x00000020   Data   RO         7175    Region$$Table       anon$$obj.o
    0x08008868   0x0000001c   Data   RO         7070    locale$$data        c_w.l(lc_numeric_c.o)
    0x08008884   0x00000004   Data   RO          828    .init_array         global_variable.o
    0x08008888   0x00000000   Data   RO         6916    .init_array         c_w.l(init_aeabi.o)


    Execution Region RW_IRAM1 (Base: 0x20000000, Size: 0x00008268, Max: 0x0001c000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x00000014   Data   RW          284    .data               ZDT.o
    0x20000014   0x0000001c   Data   RW          719    .data               freertos.o
    0x20000030   0x0000001c   Data   RW          823    .data               global_variable.o
    0x2000004c   0x00000004   Data   RW          824    .data               global_variable.o
    0x20000050   0x00000004   Data   RW          825    .data               global_variable.o
    0x20000054   0x00000008   Data   RW          826    .data               global_variable.o
    0x2000005c   0x00000008   Data   RW         1133    .data               motor.o
    0x20000064   0x00000004   Data   RW         1300    .data               oled.o
    0x20000068   0x00000004   Data   RW         1760    .data               system_stm32f4xx.o
    0x2000006c   0x0000000c   Data   RW         2186    .data               stm32f4xx_hal.o
    0x20000078   0x00000004   Data   RW         5179    .data               cmsis_os2.o
    0x2000007c   0x00000020   Data   RW         5807    .data               heap_4.o
    0x2000009c   0x0000000c   Data   RW         5875    .data               port.o
    0x200000a8   0x0000003c   Data   RW         6396    .data               tasks.o
    0x200000e4   0x00000014   Data   RW         6727    .data               timers.o
    0x200000f8   0x0000000d   Zero   RW          283    .bss                ZDT.o
    0x20000105   0x00000003   PAD
    0x20000108   0x00000200   Zero   RW          705    .bss                freertos.o
    0x20000308   0x0000005c   Zero   RW          706    .bss                freertos.o
    0x20000364   0x00000400   Zero   RW          707    .bss                freertos.o
    0x20000764   0x0000005c   Zero   RW          708    .bss                freertos.o
    0x200007c0   0x00000800   Zero   RW          709    .bss                freertos.o
    0x20000fc0   0x0000005c   Zero   RW          710    .bss                freertos.o
    0x2000101c   0x00000400   Zero   RW          711    .bss                freertos.o
    0x2000141c   0x0000005c   Zero   RW          712    .bss                freertos.o
    0x20001478   0x00000800   Zero   RW          713    .bss                freertos.o
    0x20001c78   0x0000005c   Zero   RW          714    .bss                freertos.o
    0x20001cd4   0x00000800   Zero   RW          715    .bss                freertos.o
    0x200024d4   0x0000005c   Zero   RW          716    .bss                freertos.o
    0x20002530   0x00000300   Zero   RW          822    .bss                global_variable.o
    0x20002830   0x00000054   Zero   RW          894    .bss                i2c.o
    0x20002884   0x000000a0   Zero   RW         1196    .bss                my_task.o
    0x20002924   0x00000481   Zero   RW         1299    .bss                oled.o
    0x20002da5   0x00000003   PAD
    0x20002da8   0x00000048   Zero   RW         1566    .bss                stm32f4xx_hal_timebase_tim.o
    0x20002df0   0x00000120   Zero   RW         1805    .bss                tim.o
    0x20002f10   0x00000420   Zero   RW         1921    .bss                usart.o
    0x20003330   0x000006b8   Zero   RW         5178    .bss                cmsis_os2.o
    0x200039e8   0x00003c00   Zero   RW         5806    .bss                heap_4.o
    0x200075e8   0x00000040   Zero   RW         5980    .bss                queue.o
    0x20007628   0x000004c4   Zero   RW         6395    .bss                tasks.o
    0x20007aec   0x00000118   Zero   RW         6726    .bss                timers.o
    0x20007c04   0x00000060   Zero   RW         7082    .bss                c_w.l(libspace.o)
    0x20007c64   0x00000004   PAD
    0x20007c68   0x00000200   Zero   RW         6856    HEAP                startup_stm32f407xx.o
    0x20007e68   0x00000400   Zero   RW         6855    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Base: 0x2001c000, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_IROM2 (Base: 0x00000000, Size: 0x00000000, Max: 0x00000000, ABSOLUTE)

    Execution Region ER_IROM2 (Base: 0x00000000, Size: 0x00000000, Max: 0x00000000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

         0          0          0          0          0     688704   LED_Buzzer.o
       254         18          0         20         13       3640   ZDT.o
         0          0          0          0          0        322   app_state.o
       336         38          0          0          0       1045   callback.o
       326         22          0          4       1720      68187   cmsis_os2.o
       172          4          0          0          0     700078   dma.o
         0          0          0          0          0      19504   event_groups.o
         0          0       1688          0          0       2808   font.o
       148         38        305         28       9256      22767   freertos.o
       760        112          4         44        768       2168   global_variable.o
       324         26          0          0          0       1103   gpio.o
       472         24          0         32      15360      21907   heap_4.o
       200         26          0          0         84       1701   i2c.o
       310         30          0          0          0       2808   jy901s.o
         8          0          0          0          0        872   key.o
       138          0          0          0          0       3574   list.o
       218         20          0          0          0       2568   main.o
       106         10          0          0          0       1780   maixcam.o
       712          8          0          8          0       4244   motor.o
       294         74          0          0        160       3022   my_task.o
       966         28          0          4       1153      11387   oled.o
       364         16          0          0          0       2180   pid.o
       786         82          0         12          0      11949   port.o
      1514         20          0          0         64      17456   queue.o
        64         26        392          0       1536        812   startup_stm32f407xx.o
       116         18          0         12          0       8726   stm32f4xx_hal.o
       158         14          0          0          0      33054   stm32f4xx_hal_cortex.o
      1090         16          8          0          0       7957   stm32f4xx_hal_dma.o
       522         46          0          0          0       2845   stm32f4xx_hal_gpio.o
      4636         72          0          0          0      29747   stm32f4xx_hal_i2c.o
        56          4          0          0          0        870   stm32f4xx_hal_msp.o
      1408         82          0          0          0       5913   stm32f4xx_hal_rcc.o
      1960        138          0          0          0      15183   stm32f4xx_hal_tim.o
       232         28          0          0          0       3321   stm32f4xx_hal_tim_ex.o
       152         20          0          0         72       1488   stm32f4xx_hal_timebase_tim.o
      1974         44          0          0          0      15415   stm32f4xx_hal_uart.o
       204         96          0          0          0      10441   stm32f4xx_it.o
        16          4         24          4          0       1135   system_stm32f4xx.o
      2084        168          0         60       1220      26968   tasks.o
       880         84          0          0        288       5799   tim.o
       972         80          0         20        280      12675   timers.o
        34          0          0          0          0        496   truck.o
      1108         86          0          0       1056       4673   usart.o
       370         22          0          0          0     587124   vofa.o

    ----------------------------------------------------------------------
     26510       <USER>       <GROUP>        248      33036    2370416   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        66          0          3          0          6          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       284          0          0          0          0        156   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        48          6          0          0          0         96   _printf_char_common.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
        10          0          0          0          0         68   _sputc.o
        16          0          0          0          0         68   aeabi_memset.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        36         10          0          0          0         80   init_aeabi.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        26          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
       238          0          0          0          0        100   lludivv7m.o
        88          0          0          0          0         76   memcmp.o
        40          6          0          0          0         84   noretval__2sprintf.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        62          0          0          0          0         76   strlen.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        16          0          0          0          0         68   cxa_guard_acquire.o
        12          0          0          0          0        116   dretinf.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
         4          0          0          0          0        116   printf1.o
         0          0          0          0          0          0   usenofp.o
        48          0          0          0          0        124   fpclassify.o

    ----------------------------------------------------------------------
      5810        <USER>        <GROUP>          0        100       4028   Library Totals
        14          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      5480        216        176          0         96       3224   c_w.l
        16          0          0          0          0         68   cpprt_w.l
       252          8          0          0          0        612   fz_wm.l
        48          0          0          0          0        124   m_wm.l

    ----------------------------------------------------------------------
      5810        <USER>        <GROUP>          0        100       4028   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     32320       1868       2632        248      33136    2347172   Grand Totals
     32320       1868       2632        248      33136    2347172   ELF Image Totals
     32320       1868       2632        248          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                34952 (  34.13kB)
    Total RW  Size (RW Data + ZI Data)             33384 (  32.60kB)
    Total ROM Size (Code + RO Data + RW Data)      35200 (  34.38kB)

==============================================================================

