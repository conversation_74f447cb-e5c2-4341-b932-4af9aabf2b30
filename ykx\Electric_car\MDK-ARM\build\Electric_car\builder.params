{"name": "Electric_car", "target": "Electric_car", "toolchain": "AC5", "toolchainLocation": "D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC", "toolchainCfgFile": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.23.12\\res\\data\\models/arm.v5.model.json", "buildMode": "fast|multhread", "showRepathOnLog": true, "threadNum": 20, "rootDir": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "dumpPath": "build\\Electric_car", "outDir": "build\\Electric_car", "ram": 131072, "rom": 524288, "incDirs": ["../Core/Inc", "../Drivers/STM32F4xx_HAL_Driver/Inc", "../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy", "../Middlewares/Third_Party/FreeRTOS/Source/include", "../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2", "../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F", "../Drivers/CMSIS/Device/ST/STM32F4xx/Include", "../Drivers/CMSIS/Include", ".cmsis/include", "RTE/_Electric_car"], "libDirs": [], "defines": ["USE_HAL_DRIVER", "STM32F407xx"], "sourceList": ["../Core/Src/LED_Buzzer.cpp", "../Core/Src/ZDT.cpp", "../Core/Src/app_state.cpp", "../Core/Src/callback.cpp", "../Core/Src/dma.c", "../Core/Src/font.cpp", "../Core/Src/freertos.c", "../Core/Src/global_variable.cpp", "../Core/Src/gpio.c", "../Core/Src/i2c.c", "../Core/Src/jy901s.cpp", "../Core/Src/key.cpp", "../Core/Src/main.cpp", "../Core/Src/maixcam.cpp", "../Core/Src/motor.cpp", "../Core/Src/my_task.cpp", "../Core/Src/oled.cpp", "../Core/Src/pid.cpp", "../Core/Src/servo.cpp", "../Core/Src/stm32f4xx_hal_msp.c", "../Core/Src/stm32f4xx_hal_timebase_tim.c", "../Core/Src/stm32f4xx_it.c", "../Core/Src/system_stm32f4xx.c", "../Core/Src/system_stm32f4xx.c", "../Core/Src/tim.c", "../Core/Src/truck.cpp", "../Core/Src/usart.c", "../Core/Src/vofa.cpp", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c", "../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c", "../Middlewares/Third_Party/FreeRTOS/Source/croutine.c", "../Middlewares/Third_Party/FreeRTOS/Source/event_groups.c", "../Middlewares/Third_Party/FreeRTOS/Source/list.c", "../Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c", "../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/port.c", "../Middlewares/Third_Party/FreeRTOS/Source/queue.c", "../Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c", "../Middlewares/Third_Party/FreeRTOS/Source/tasks.c", "../Middlewares/Third_Party/FreeRTOS/Source/timers.c", "startup_stm32f407xx.s"], "alwaysInBuildSources": [], "sourceParams": {}, "options": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [{"name": "axf to elf", "command": "axf2elf -d \"${ToolchainRoot}\" -i \"${OutDir}/${ProjectName}.axf\" -o \"${OutDir}/${ProjectName}.elf\" > \"${OutDir}/axf2elf.log\""}], "global": {"use-microLIB": false, "output-debug-info": "enable", "microcontroller-cpu": "cortex-m4-sp", "microcontroller-fpu": "cortex-m4-sp", "microcontroller-float": "cortex-m4-sp", "$arch-extensions": "", "$clang-arch-extensions": "", "$armlink-arch-extensions": ""}, "c/cpp-compiler": {"optimization": "level-3", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf", "xo-base": "", "ro-base": "", "rw-base": "", "link-scatter": ["e:/STM32TESTPROJECT/ykx/Electric_car/MDK-ARM/build/Electric_car/Electric_car.sct"]}}, "env": {"KEIL_OUTPUT_DIR": "Electric_car", "workspaceFolder": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "workspaceFolderBasename": "MDK-ARM", "OutDir": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM\\build\\Electric_car", "OutDirRoot": "build", "OutDirBase": "build\\Electric_car", "ProjectName": "Electric_car", "ConfigName": "Electric_car", "ProjectRoot": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM", "ExecutableName": "e:\\STM32TESTPROJECT\\ykx\\Electric_car\\MDK-ARM\\build\\Electric_car\\Electric_car", "ChipPackDir": "", "ChipName": "", "SYS_Platform": "win32", "SYS_DirSep": "\\", "SYS_DirSeparator": "\\", "SYS_PathSep": ";", "SYS_PathSeparator": ";", "SYS_EOL": "\r\n", "EIDE_BUILDER_DIR": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.23.12\\res\\tools\\win32\\unify_builder", "EIDE_BINARIES_VER": "12.1.1", "EIDE_MSYS": "C:\\Users\\<USER>\\.eide\\bin\\builder\\msys\\bin", "EIDE_PY3_CMD": "C:\\Users\\<USER>\\.eide\\bin\\python36\\python3.exe", "ToolchainRoot": "D:\\keil5-stm32.51\\Keil5 MDK\\ARM\\ARMCC"}, "sysPaths": []}